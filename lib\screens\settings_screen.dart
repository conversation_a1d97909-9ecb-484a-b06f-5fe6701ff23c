import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../widgets/bottom_navigation.dart';
import '../widgets/adaptive_widgets.dart';

class SettingsScreen extends StatefulWidget {
    const SettingsScreen({super.key});

    @override
    State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
    @override
    Widget build(BuildContext context) {
        return Scaffold(
            backgroundColor: Colors.grey[50],
            body: Column(
                children: [
                    _buildHeader(),
                    Expanded(
                        child: SingleChildScrollView(
                            child: Padding(
                                padding: const EdgeInsets.fromLTRB(16, 0, 16, 100),
                                child: Column(
                                    children: [
                                        _buildPreferencesSection(),
                                        const SizedBox(height: 24),
                                        _buildHelpSection(),
                                        const SizedBox(height: 24),
                                        _buildAboutSection(),
                                    ],
                                ),
                            ),
                        ),
                    ),
                ],
            ),
            bottomNavigationBar: Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                    return AdaptiveBottomNavigation(
                        currentIndex: 3,
                        onTap: appState.setBottomNavIndex,
                    );
                },
            ),
        );
    }

    Widget _buildHeader() {
        return Container(
            height: 200,
            decoration: const BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                        Color(0xFFA855F7), // Purple-400
                        Color(0xFF9333EA), // Purple-500
                        Color(0xFF6366F1), // Indigo-500
                    ],
                ),
            ),
            child: SafeArea(
                child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                        children: [
                            Row(
                                children: [
                                    Container(
                                        width: 40,
                                        height: 40,
                                        decoration: BoxDecoration(
                                            color: Colors.white.withOpacity(0.5),
                                            borderRadius: BorderRadius.circular(20),
                                        ),
                                        child: IconButton(
                                            onPressed: () => context.go('/home'),
                                            icon: const Icon(
                                                Icons.arrow_back,
                                                color: Colors.black87,
                                            ),
                                        ),
                                    ),
                                    const Expanded(
                                        child: Text(
                                            'Settings',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                fontSize: 20,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.black87,
                                            ),
                                        ),
                                    ),
                                    const SizedBox(width: 40), // Balance the back button
                                ],
                            ),
                        ],
                    ),
                ),
            ),
        );
    }

    Widget _buildPreferencesSection() {
        return Transform.translate(
            offset: const Offset(0, -128),
            child: Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                        BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                        ),
                    ],
                ),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                        const Padding(
                            padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
                            child: Text(
                                'Preferences',
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey,
                                ),
                            ),
                        ),
                        _buildSettingsItem(
                            icon: Icons.language,
                            iconColor: Colors.indigo,
                            title: 'Language',
                            subtitle: 'English',
                            onTap: () => _showLanguageDialog(),
                        ),
                        _buildSettingsItem(
                            icon: Icons.contrast,
                            iconColor: Colors.indigo,
                            title: 'Theme',
                            subtitle: 'System',
                            onTap: () => _showThemeDialog(),
                        ),
                        _buildSettingsItem(
                            icon: Icons.delete,
                            iconColor: Colors.indigo,
                            title: 'Clear Cache',
                            onTap: () => _clearCache(),
                            showDivider: false,
                        ),
                    ],
                ),
            ),
        );
    }

    Widget _buildHelpSection() {
        return Transform.translate(
            offset: const Offset(0, -128),
            child: Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                        BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                        ),
                    ],
                ),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                        const Padding(
                            padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
                            child: Text(
                                'Help',
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey,
                                ),
                            ),
                        ),
                        _buildSettingsItem(
                            icon: Icons.lightbulb,
                            iconColor: Colors.teal,
                            title: 'Prompt Best Practices',
                            onTap: () => _showPromptTips(),
                        ),
                        _buildSettingsItem(
                            icon: Icons.support_agent,
                            iconColor: Colors.teal,
                            title: 'Customer Service',
                            onTap: () => _contactSupport(),
                            showDivider: false,
                        ),
                    ],
                ),
            ),
        );
    }

    Widget _buildAboutSection() {
        return Transform.translate(
            offset: const Offset(0, -128),
            child: Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                        BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                        ),
                    ],
                ),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                        const Padding(
                            padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
                            child: Text(
                                'About',
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey,
                                ),
                            ),
                        ),
                        _buildSettingsItem(
                            icon: Icons.info,
                            iconColor: Colors.grey,
                            title: 'About this app',
                            onTap: () => _showAboutDialog(),
                            showDivider: false,
                        ),
                    ],
                ),
            ),
        );
    }

    Widget _buildSettingsItem({
        required IconData icon,
        required Color iconColor,
        required String title,
        String? subtitle,
        required VoidCallback onTap,
        bool showDivider = true,
    }) {
        return Column(
            children: [
                ListTile(
                    leading: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                            color: iconColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                        ),
                        child: Icon(
                            icon,
                            color: iconColor,
                            size: 20,
                        ),
                    ),
                    title: Text(
                        title,
                        style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                        ),
                    ),
                    subtitle: subtitle != null
                        ? Text(
                            subtitle,
                            style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                            ),
                        )
                        : null,
                    trailing: const Icon(
                        Icons.chevron_right,
                        color: Colors.grey,
                    ),
                    onTap: onTap,
                ),
                if (showDivider)
                    Divider(
                        height: 1,
                        indent: 64,
                        color: Colors.grey[200],
                    ),
            ],
        );
    }

    void _showLanguageDialog() {
        showDialog(
            context: context,
            builder: (context) => AdaptiveDialog(
                title: 'Language',
                content: 'Select your preferred language',
                actions: [
                    AdaptiveDialogAction(
                        text: 'English',
                        onPressed: () => Navigator.of(context).pop(),
                        isDefault: true,
                    ),
                    AdaptiveDialogAction(
                        text: 'Cancel',
                        onPressed: () => Navigator.of(context).pop(),
                    ),
                ],
            ),
        );
    }

    void _showThemeDialog() {
        showDialog(
            context: context,
            builder: (context) => AdaptiveDialog(
                title: 'Theme',
                content: 'Choose your preferred theme',
                actions: [
                    AdaptiveDialogAction(
                        text: 'Light',
                        onPressed: () {
                            Provider.of<AppStateProvider>(context, listen: false)
                                .setThemeMode(ThemeMode.light);
                            Navigator.of(context).pop();
                        },
                    ),
                    AdaptiveDialogAction(
                        text: 'Dark',
                        onPressed: () {
                            Provider.of<AppStateProvider>(context, listen: false)
                                .setThemeMode(ThemeMode.dark);
                            Navigator.of(context).pop();
                        },
                    ),
                    AdaptiveDialogAction(
                        text: 'System',
                        onPressed: () {
                            Provider.of<AppStateProvider>(context, listen: false)
                                .setThemeMode(ThemeMode.system);
                            Navigator.of(context).pop();
                        },
                        isDefault: true,
                    ),
                    AdaptiveDialogAction(
                        text: 'Cancel',
                        onPressed: () => Navigator.of(context).pop(),
                    ),
                ],
            ),
        );
    }

    void _clearCache() {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Cache cleared successfully'),
                backgroundColor: Colors.green,
            ),
        );
    }

    void _showPromptTips() {
        showDialog(
            context: context,
            builder: (context) => AdaptiveDialog(
                title: 'Prompt Best Practices',
                content: 'Be specific and descriptive in your prompts for better results.',
                actions: [
                    AdaptiveDialogAction(
                        text: 'Got it',
                        onPressed: () => Navigator.of(context).pop(),
                        isDefault: true,
                    ),
                ],
            ),
        );
    }

    void _contactSupport() {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Opening customer support...'),
                backgroundColor: Colors.blue,
            ),
        );
    }

    void _showAboutDialog() {
        showDialog(
            context: context,
            builder: (context) => AdaptiveDialog(
                title: 'About NanoBanana AI',
                content: 'Version 1.0.0\nGenerate stunning visuals in seconds.',
                actions: [
                    AdaptiveDialogAction(
                        text: 'OK',
                        onPressed: () => Navigator.of(context).pop(),
                        isDefault: true,
                    ),
                ],
            ),
        );
    }
}
