#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
插件數量測試工具
快速檢查插件總數和數據完整性
"""

import sys
import os

# 添加上級目錄到路径，以便導入lib模塊
sys.path.append('..')

def test_plugin_data():
    """測試插件數據"""
    print("🚀 測試插件數據...")
    
    try:
        # 嘗試導入插件數據
        from lib.data.plugin_data import allPlugins
        
        print(f"✅ 成功導入插件數據")
        print(f"   總插件數量: {len(allPlugins)}")
        
        # 檢查每個插件的完整性
        valid_plugins = 0
        edit_count = 0
        generate_count = 0
        
        for i, plugin in enumerate(allPlugins):
            # 檢查必需字段
            required_fields = ['name', 'author', 'imageUrl', 'prompt', 'mode']
            is_valid = all(hasattr(plugin, field) and getattr(plugin, field) for field in required_fields)
            
            if is_valid:
                valid_plugins += 1
                if plugin.mode == 'Edit':
                    edit_count += 1
                elif plugin.mode == 'Generate':
                    generate_count += 1
            else:
                print(f"⚠️  插件 {i + 1} 有空字段")
        
        print(f"   有效插件數量: {valid_plugins}")
        print(f"   Edit模式: {edit_count} 個")
        print(f"   Generate模式: {generate_count} 個")
        
        # 顯示前5個和後5個
        print(f"\n📋 前5個插件:")
        for i in range(min(5, len(allPlugins))):
            plugin = allPlugins[i]
            print(f"   {i+1}. {plugin.name} by {plugin.author} ({plugin.mode})")
        
        print(f"\n📋 後5個插件:")
        start = max(0, len(allPlugins) - 5)
        for i in range(start, len(allPlugins)):
            plugin = allPlugins[i]
            print(f"   {i+1}. {plugin.name} by {plugin.author} ({plugin.mode})")
        
        # 評估結果
        if len(allPlugins) >= 105:
            print(f"\n🎉 優秀！達到目標 {len(allPlugins)} 個插件")
        elif len(allPlugins) >= 100:
            print(f"\n✅ 良好！{len(allPlugins)} 個插件，接近目標")
        else:
            print(f"\n⚠️  {len(allPlugins)} 個插件，少於預期")
        
        if valid_plugins == len(allPlugins):
            print("✅ 所有插件數據完整！")
        else:
            print(f"⚠️  有 {len(allPlugins) - valid_plugins} 個插件數據不完整")
        
        return True
        
    except ImportError as e:
        print(f"❌ 無法導入插件數據: {e}")
        print("   請確保 ../lib/data/plugin_data.dart 文件存在且格式正確")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_with_dart():
    """使用Dart直接測試"""
    print("\n🔍 使用Dart測試...")
    
    dart_test_code = '''
import '../lib/data/plugin_data.dart';

void main() {
  print('總插件數量: \${allPlugins.length}');
  
  int validPlugins = 0;
  int editCount = 0;
  int generateCount = 0;
  
  for (int i = 0; i < allPlugins.length; i++) {
    final plugin = allPlugins[i];
    
    bool isValid = plugin.name.isNotEmpty &&
                   plugin.author.isNotEmpty &&
                   plugin.imageUrl.isNotEmpty &&
                   plugin.prompt.isNotEmpty &&
                   plugin.mode.isNotEmpty;
    
    if (isValid) {
      validPlugins++;
      if (plugin.mode == 'Edit') {
        editCount++;
      } else if (plugin.mode == 'Generate') {
        generateCount++;
      }
    }
  }
  
  print('有效插件數量: \$validPlugins');
  print('Edit模式: \$editCount 個');
  print('Generate模式: \$generateCount 個');
  
  if (validPlugins == allPlugins.length) {
    print('✅ 所有插件數據完整！');
  } else {
    print('⚠️  有 \${allPlugins.length - validPlugins} 個插件數據不完整');
  }
}
'''
    
    # 寫入臨時測試文件
    with open('../temp_test.dart', 'w', encoding='utf-8') as f:
        f.write(dart_test_code)
    
    # 運行Dart測試
    import subprocess
    try:
        result = subprocess.run(['dart', '../temp_test.dart'], 
                              capture_output=True, text=True, cwd='..')
        if result.returncode == 0:
            print("✅ Dart測試成功:")
            print(result.stdout)
        else:
            print("❌ Dart測試失敗:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 無法運行Dart測試: {e}")
    finally:
        # 清理臨時文件
        if os.path.exists('../temp_test.dart'):
            os.remove('../temp_test.dart')

def main():
    """主函數"""
    print("🧪 插件數量測試工具\n")
    
    # Python測試（如果可能）
    success = test_plugin_data()
    
    # Dart測試
    if not success:
        test_with_dart()
    
    print(f"\n🎯 測試完成")
    print("   如需重新生成數據，請運行: python generate_plugin_data.py")
    print("   如需驗證數據，請運行: python verify_plugin_data.py")

if __name__ == "__main__":
    main()
