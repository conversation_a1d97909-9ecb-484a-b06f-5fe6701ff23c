<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<meta charset="utf-8"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-color: #7B61FF;}
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-white">
<div class="relative flex size-full min-h-screen flex-col justify-between group/design-root overflow-x-hidden" style='font-family: "Spline Sans", "Noto Sans", sans-serif; background: linear-gradient(180deg, #FDFCFB 0%, #E2DFFF 100%);'>
<div class="flex-grow">
<div class="flex items-center p-4 pb-2 justify-between sticky top-0 z-10" style="background: linear-gradient(180deg, #FDFCFB 0%, #E2DFFF 20%);">
<div class="flex-1"></div>
<h2 class="text-black text-xl font-bold leading-tight tracking-[-0.015em] flex-1 text-center">Plugins</h2>
<div class="flex w-12 items-center justify-end flex-1">
<button class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 w-10 bg-transparent text-black gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0 hover:bg-black/10">
<div class="text-black" data-icon="Plus" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z"></path>
</svg>
</div>
</button>
</div>
</div>
<div class="px-4 py-3">
<label class="flex flex-col min-w-40 h-12 w-full">
<div class="flex w-full flex-1 items-stretch rounded-full h-full shadow-sm">
<div class="text-gray-500 flex border-none bg-white items-center justify-center pl-4 rounded-l-full border-r-0" data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
</svg>
</div>
<input class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-r-full text-black focus:outline-0 focus:ring-0 border-none bg-white focus:border-none h-full placeholder:text-gray-500 px-4 text-base font-normal leading-normal" placeholder="Search plugins..." value=""/>
</div>
</label>
</div>
<div class="flex gap-3 px-4 py-2">
<button class="flex h-10 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[var(--primary-color)] px-5 text-sm font-bold text-white">Collect</button>
<button class="flex h-10 shrink-0 items-center justify-center gap-x-2 rounded-full bg-white/80 px-5 text-sm font-medium text-black hover:bg-white">Generate</button>
<button class="flex h-10 shrink-0 items-center justify-center gap-x-2 rounded-full bg-white/80 px-5 text-sm font-medium text-black hover:bg-white">Edit</button>
</div>
<div class="grid grid-cols-2 gap-4 p-4">
<div class="flex flex-col gap-2 rounded-2xl bg-white/80 overflow-hidden shadow-sm backdrop-blur-sm relative">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBaN48K7NgRwofVt_hk3M3_46TX2Fk4K8x8dqwxier-CyIkJZc6-PRBbWFtrridAxxZbH8lj8xrUs2LfaB5HKS_HJNthwaZ-CHgsvDjzxUX5rqR7DKlCoItJTD2XRiBbJjPP7XX194HUAuO8eHMhyJIXreMqWQY9MNQ5XjIzfk_R8KCZuX7JT8EFKKR2r32JZBopI1IHIP2jBWilP0Jc_MaBCrlVR42QHdfsFFElX4qnP3bNdSvcpXZdnTPpv-LS9GpqLV0ANwn-aLg");'></div>
<button class="absolute top-2 right-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/50 backdrop-blur-sm text-gray-700 hover:text-red-500">
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" fill-rule="evenodd"></path></svg>
</button>
<div class="p-3 pt-1">
<p class="text-black text-base font-bold leading-normal">Surrealism</p>
<p class="text-gray-500 text-sm font-normal leading-normal">@dall-e</p>
<div class="flex justify-end">
<button class="text-sm font-medium text-[var(--primary-color)]">Edit</button>
</div>
</div>
</div>
<div class="flex flex-col gap-2 rounded-2xl bg-white/80 overflow-hidden shadow-sm backdrop-blur-sm relative">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBaN48K7NgRwofVt_hk3M3_46TX2Fk4K8x8dqwxier-CyIkJZc6-PRBbWFtrridAxxZbH8lj8xrUs2LfaB5HKS_HJNthwaZ-CHgsvDjzxUX5rqR7DKlCoItJTD2XRiBbJjPP7XX194HUAuO8eHMhyJIXreMqWQY9MNQ5XjIzfk_R8KCZuX7JT8EFKKR2r32JZBopI1IHIP2jBWilP0Jc_MaBCrlVR42QHdfsFFElX4qnP3bNdSvcpXZdnTPpv-LS9GpqLV0ANwn-aLg");'></div>
<button class="absolute top-2 right-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/50 backdrop-blur-sm text-red-500">
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" fill-rule="evenodd"></path></svg>
</button>
<div class="p-3 pt-1">
<p class="text-black text-base font-bold leading-normal">Anime v2</p>
<p class="text-gray-500 text-sm font-normal leading-normal">@midjourney</p>
<div class="flex justify-end">
<button class="text-sm font-medium text-[var(--primary-color)]">Edit</button>
</div>
</div>
</div>
<div class="flex flex-col gap-2 rounded-2xl bg-white/80 overflow-hidden shadow-sm backdrop-blur-sm relative">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBaN48K7NgRwofVt_hk3M3_46TX2Fk4K8x8dqwxier-CyIkJZc6-PRBbWFtrridAxxZbH8lj8xrUs2LfaB5HKS_HJNthwaZ-CHgsvDjzxUX5rqR7DKlCoItJTD2XRiBbJjPP7XX194HUAuO8eHMhyJIXreMqWQY9MNQ5XjIzfk_R8KCZuX7JT8EFKKR2r32JZBopI1IHIP2jBWilP0Jc_MaBCrlVR42QHdfsFFElX4qnP3bNdSvcpXZdnTPpv-LS9GpqLV0ANwn-aLg");'></div>
<button class="absolute top-2 right-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/50 backdrop-blur-sm text-gray-700 hover:text-red-500">
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" fill-rule="evenodd"></path></svg>
</button>
<div class="p-3 pt-1">
<p class="text-black text-base font-bold leading-normal">Portraits</p>
<p class="text-gray-500 text-sm font-normal leading-normal">@artbreeder</p>
<div class="flex justify-end">
<button class="text-sm font-medium text-[var(--primary-color)]">Edit</button>
</div>
</div>
</div>
<div class="flex flex-col gap-2 rounded-2xl bg-white/80 overflow-hidden shadow-sm backdrop-blur-sm relative">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBaN48K7NgRwofVt_hk3M3_46TX2Fk4K8x8dqwxier-CyIkJZc6-PRBbWFtrridAxxZbH8lj8xrUs2LfaB5HKS_HJNthwaZ-CHgsvDjzxUX5rqR7DKlCoItJTD2XRiBbJjPP7XX194HUAuO8eHMhyJIXreMqWQY9MNQ5XjIzfk_R8KCZuX7JT8EFKKR2r32JZBopI1IHIP2jBWilP0Jc_MaBCrlVR42QHdfsFFElX4qnP3bNdSvcpXZdnTPpv-LS9GpqLV0ANwn-aLg");'></div>
<button class="absolute top-2 right-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/50 backdrop-blur-sm text-gray-700 hover:text-red-500">
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" fill-rule="evenodd"></path></svg>
</button>
<div class="p-3 pt-1">
<p class="text-black text-base font-bold leading-normal">Landscapes</p>
<p class="text-gray-500 text-sm font-normal leading-normal">@stablediffusion</p>
<div class="flex justify-end">
<button class="text-sm font-medium text-[var(--primary-color)]">Edit</button>
</div>
</div>
</div>
<div class="flex flex-col gap-2 rounded-2xl bg-white/80 overflow-hidden shadow-sm backdrop-blur-sm relative">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBaN48K7NgRwofVt_hk3M3_46TX2Fk4K8x8dqwxier-CyIkJZc6-PRBbWFtrridAxxZbH8lj8xrUs2LfaB5HKS_HJNthwaZ-CHgsvDjzxUX5rqR7DKlCoItJTD2XRiBbJjPP7XX194HUAuO8eHMhyJIXreMqWQY9MNQ5XjIzfk_R8KCZuX7JT8EFKKR2r32JZBopI1IHIP2jBWilP0Jc_MaBCrlVR42QHdfsFFElX4qnP3bNdSvcpXZdnTPpv-LS9GpqLV0ANwn-aLg");'></div>
<button class="absolute top-2 right-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/50 backdrop-blur-sm text-red-500">
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" fill-rule="evenodd"></path></svg>
</button>
<div class="p-3 pt-1">
<p class="text-black text-base font-bold leading-normal">Sci-Fi</p>
<p class="text-gray-500 text-sm font-normal leading-normal">@wombo</p>
<div class="flex justify-end">
<button class="text-sm font-medium text-[var(--primary-color)]">Edit</button>
</div>
</div>
</div>
<div class="flex flex-col gap-2 rounded-2xl bg-white/80 overflow-hidden shadow-sm backdrop-blur-sm relative">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBaN48K7NgRwofVt_hk3M3_46TX2Fk4K8x8dqwxier-CyIkJZc6-PRBbWFtrridAxxZbH8lj8xrUs2LfaB5HKS_HJNthwaZ-CHgsvDjzxUX5rqR7DKlCoItJTD2XRiBbJjPP7XX194HUAuO8eHMhyJIXreMqWQY9MNQ5XjIzfk_R8KCZuX7JT8EFKKR2r32JZBopI1IHIP2jBWilP0Jc_MaBCrlVR42QHdfsFFElX4qnP3bNdSvcpXZdnTPpv-LS9GpqLV0ANwn-aLg");'></div>
<button class="absolute top-2 right-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/50 backdrop-blur-sm text-gray-700 hover:text-red-500">
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" fill-rule="evenodd"></path></svg>
</button>
<div class="p-3 pt-1">
<p class="text-black text-base font-bold leading-normal">Abstract</p>
<p class="text-gray-500 text-sm font-normal leading-normal">@nightcafe</p>
<div class="flex justify-end">
<button class="text-sm font-medium text-[var(--primary-color)]">Edit</button>
</div>
</div>
</div>
<div class="flex flex-col gap-2 rounded-2xl bg-white/80 overflow-hidden shadow-sm backdrop-blur-sm relative">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBaN48K7NgRwofVt_hk3M3_46TX2Fk4K8x8dqwxier-CyIkJZc6-PRBbWFtrridAxxZbH8lj8xrUs2LfaB5HKS_HJNthwaZ-CHgsvDjzxUX5rqR7DKlCoItJTD2XRiBbJjPP7XX194HUAuO8eHMhyJIXreMqWQY9MNQ5XjIzfk_R8KCZuX7JT8EFKKR2r32JZBopI1IHIP2jBWilP0Jc_MaBCrlVR42QHdfsFFElX4qnP3bNdSvcpXZdnTPpv-LS9GpqLV0ANwn-aLg");'></div>
<button class="absolute top-2 right-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/50 backdrop-blur-sm text-gray-700 hover:text-red-500">
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" fill-rule="evenodd"></path></svg>
</button>
<div class="p-3 pt-1">
<p class="text-black text-base font-bold leading-normal">Fantasy</p>
<p class="text-gray-500 text-sm font-normal leading-normal">@deepdream</p>
<div class="flex justify-end">
<button class="text-sm font-medium text-[var(--primary-color)]">Edit</button>
</div>
</div>
</div>
<div class="flex flex-col gap-2 rounded-2xl bg-white/80 overflow-hidden shadow-sm backdrop-blur-sm relative">
<div class="w-full bg-center bg-no-repeat aspect-square bg-cover" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBaN48K7NgRwofVt_hk3M3_46TX2Fk4K8x8dqwxier-CyIkJZc6-PRBbWFtrridAxxZbH8lj8xrUs2LfaB5HKS_HJNthwaZ-CHgsvDjzxUX5rqR7DKlCoItJTD2XRiBbJjPP7XX194HUAuO8eHMhyJIXreMqWQY9MNQ5XjIzfk_R8KCZuX7JT8EFKKR2r32JZBopI1IHIP2jBWilP0Jc_MaBCrlVR42QHdfsFFElX4qnP3bNdSvcpXZdnTPpv-LS9GpqLV0ANwn-aLg");'></div>
<button class="absolute top-2 right-2 flex items-center justify-center w-8 h-8 rounded-full bg-white/50 backdrop-blur-sm text-gray-700 hover:text-red-500">
<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" fill-rule="evenodd"></path></svg>
</button>
<div class="p-3 pt-1">
<p class="text-black text-base font-bold leading-normal">Pop Art</p>
<p class="text-gray-500 text-sm font-normal leading-normal">@artisto</p>
<div class="flex justify-end">
<button class="text-sm font-medium text-[var(--primary-color)]">Edit</button>
</div>
</div>
</div>
</div>
</div>
<div class="sticky bottom-0 bg-white/80 backdrop-blur-sm rounded-t-3xl">
<div class="flex gap-2 border-t border-gray-200 px-4 pb-3 pt-2">
<a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[var(--primary-color)]" href="#">
<div class="text-[var(--primary-color)] flex h-8 w-16 items-center justify-center rounded-full bg-purple-100" data-icon="House" data-size="24px" data-weight="fill">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M224,115.55V208a16,16,0,0,1-16,16H168a16,16,0,0,1-16-16V168a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V115.55a16,16,0,0,1,5.17-11.78l80-75.48.11-.11a16,16,0,0,1,21.53,0,1.14,1.14,0,0,0,.11.11l80,75.48A16,16,0,0,1,224,115.55Z"></path>
</svg>
</div>
<p class="text-[var(--primary-color)] text-xs font-bold leading-normal tracking-[0.015em]">Home</p>
</a>
<a class="just flex flex-1 flex-col items-center justify-end gap-1 text-gray-500" href="#">
<div class="text-gray-500 flex h-8 items-center justify-center" data-icon="ClockCounterClockwise" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M136,80v43.47l36.12,21.67a8,8,0,0,1-8.24,13.72l-40-24A8,8,0,0,1,120,128V80a8,8,0,0,1,16,0Zm-8-48A95.44,95.44,0,0,0,60.08,60.15C52.81,67.51,46.35,74.59,40,82V64a8,8,0,0,0-16,0v40a8,8,0,0,0,8,8H72a8,8,0,0,0,0-16H49c7.15-8.42,14.27-16.35,22.39-24.57a80,80,0,1,1,1.66,114.75,8,8,0,1,0-11,11.64A96,96,0,1,0,128,32Z"></path>
</svg>
</div>
<p class="text-gray-500 text-xs font-medium leading-normal tracking-[0.015em]">History</p>
</a>
<a class="just flex flex-1 flex-col items-center justify-end gap-1 text-gray-500" href="#">
<div class="text-gray-500 flex h-8 items-center justify-center" data-icon="Sparkle" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg"><path d="M244,128a11.83,11.83,0,0,1-12,12,11.83,11.83,0,0,1-12-12,11.83,11.83,0,0,1,12-12,11.83,11.83,0,0,1,12,12ZM160,40a12,12,0,1,0,12,12A11.83,11.83,0,0,0,160,40ZM207.61,64.2a12,12,0,1,0,17,17l36.77-36.77a12,12,0,1,0-17-17ZM128,20a12,12,0,1,0,12,12A11.83,11.83,0,0,0,128,20Zm-16,84a28,28,0,1,1-28,28,28,28,0,0,1,28-28Zm104,44a12,12,0,1,0,12,12,11.83,11.83,0,0,0-12-12ZM48.44,190.66a12,12,0,1,0,17,17l36.77-36.77a12,12,0,0,0-17-17ZM96,216a12,12,0,1,0,12,12,11.83,11.83,0,0,0-12-12Z"></path></svg>
</div>
<p class="text-gray-500 text-xs font-medium leading-normal tracking-[0.015em]">Generate</p>
</a>
<a class="just flex flex-1 flex-col items-center justify-end gap-1 text-gray-500" href="#">
<div class="text-gray-500 flex h-8 items-center justify-center" data-icon="User" data-size="24px" data-weight="regular">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"></path>
</svg>
</div>
<p class="text-gray-500 text-xs font-medium leading-normal tracking-[0.015em]">Profile</p>
</a>
</div>
<div class="h-5 bg-white/80 backdrop-blur-sm"></div>
</div>
</div>
</body></html>