# NanoBanana AI - Flutter App

一個基於Flutter的AI圖像生成應用程序，支持iOS和Android平台，採用iOS-first設計理念。

## 🚀 功能特色

### 核心功能
- **Splash Screen**: 帶有動畫效果的啟動畫面
- **Plugin Marketplace**: 插件市場主頁，展示各種AI生成風格
- **Multi-Image Input**: 多圖像輸入界面，支持參考圖片選擇和提示詞輸入
- **Membership**: 會員訂閱和積分購買系統
- **Settings**: 設置頁面，包含偏好設置、幫助和關於信息

### 技術特色
- **跨平台適配**: iOS和Android原生體驗
- **響應式設計**: 適配不同屏幕尺寸
- **狀態管理**: 使用Provider進行狀態管理
- **路由導航**: 使用GoRouter進行頁面導航
- **圖片選擇**: 支持從相冊選擇圖片

## 📱 應用流程

1. **啟動畫面** (`/splash`) - 應用啟動時的歡迎界面
2. **插件市場主頁** (`/home`) - 瀏覽和選擇AI生成風格
3. **圖像創建** (`/create`) - 選擇參考圖片並輸入提示詞
4. **會員中心** (`/membership`) - 管理訂閱和購買積分
5. **設置頁面** (`/settings`) - 應用設置和幫助信息

## 🏗️ 項目結構

```
lib/
├── main.dart                    # 應用入口
├── app.dart                     # 應用配置和路由
├── providers/
│   └── app_state_provider.dart  # 狀態管理
├── screens/
│   ├── splash_screen.dart       # 啟動畫面
│   ├── plugin_marketplace_home_screen.dart  # 主頁
│   ├── multi_image_input_screen.dart        # 圖像輸入
│   ├── membership_screen.dart   # 會員中心
│   └── settings_screen.dart     # 設置頁面
└── widgets/
    ├── adaptive_widgets.dart    # 適配性組件
    └── bottom_navigation.dart   # 底部導航
```

## 🛠️ 技術棧

### 核心依賴
- **Flutter SDK**: ^3.9.2
- **go_router**: ^14.6.2 - 路由管理
- **provider**: ^6.1.2 - 狀態管理
- **image_picker**: ^1.1.2 - 圖片選擇
- **cached_network_image**: ^3.4.1 - 網絡圖片緩存

### UI組件
- **flutter_staggered_grid_view**: ^0.7.0 - 瀑布流佈局
- **flutter_platform_widgets**: ^7.0.1 - 平台適配組件

## 🎨 設計理念

### iOS-First 適配策略
1. **導航欄**
   - iOS: CupertinoNavigationBar
   - Android: AppBar

2. **按鈕**
   - iOS: CupertinoButton
   - Android: ElevatedButton

3. **對話框**
   - iOS: CupertinoAlertDialog
   - Android: AlertDialog

4. **開關**
   - 統一使用 Switch.adaptive

### 顏色主題
- **主色調**: #7B61FF (紫色)
- **背景漸變**: #FDFCFB → #E2DFFF
- **強調色**: #4913EC (深紫色)

## 🚀 快速開始

### 環境要求
- Flutter SDK 3.9.2 或更高版本
- Dart SDK 3.0.0 或更高版本
- Android Studio / VS Code
- iOS開發需要Xcode (macOS)
- Android SDK 和 Android Emulator

### 安裝步驟

1. **克隆項目**
```bash
git clone <repository-url>
cd nano_banana
```

2. **安裝依賴**
```bash
flutter pub get
```

3. **檢查Flutter環境**
```bash
flutter doctor
```

### 🔧 編譯和運行流程

#### **方法一：使用自動化腳本 (推薦)**

**Windows用戶**：
```bash
# 推薦：簡潔高效的運行腳本 (已修復目錄問題)
.\bat\run-android.bat

# 完整版自動化腳本 (包含詳細檢查和錯誤處理)
.\bat\build-and-run-android-simple.bat

# 快速運行腳本 (簡化版本)
.\bat\quick-android-run.bat

# 完整版腳本 (最詳細的日誌)
.\bat\build-and-run-android.bat
```

**✅ 修復的問題**：
- ✅ **支持雙擊直接運行** - 無需在特定目錄執行
- ✅ **支持 `.\bat\*.bat` 命令** - 從任何位置都能正確執行
- ✅ **自動目錄切換** - 腳本會自動切換到正確的項目根目錄
- ✅ **避免編碼問題** - 使用純英文避免中文字符問題

**腳本功能**：
- ✅ 自動檢查Flutter環境
- ✅ 清理項目緩存
- ✅ 安裝項目依賴
- ✅ 啟動Android模擬器
- ✅ 編譯並運行應用
- ✅ 提供詳細的成功/錯誤信息

#### **方法二：手動命令**

**1. 檢查可用設備**
```bash
flutter devices
```

**2. 檢查可用模擬器**
```bash
flutter emulators
```

**3. 啟動Android模擬器**
```bash
# 啟動指定模擬器 (替換為您的模擬器ID)
flutter emulators --launch Medium_Phone_API_36.0
```

**4. 運行應用程序**
```bash
# 在指定設備上運行
flutter run -d emulator-5554

# 或者讓Flutter自動選擇設備
flutter run
```

#### **方法三：VS Code中運行**

1. **打開VS Code**
2. **打開項目資料夾**
3. **在終端中執行**：
   ```bash
   flutter emulators --launch Medium_Phone_API_36.0
   flutter run -d emulator-5554
   ```
4. **使用熱重載**：
   - `r` - 熱重載
   - `R` - 熱重啟
   - `q` - 退出應用

### 📱 平台特定運行

#### **Android**
```bash
# 運行在Android設備/模擬器
flutter run -d android

# 構建APK
flutter build apk --release

# 構建AAB (Google Play)
flutter build appbundle --release
```

#### **iOS** (需要macOS)
```bash
# 運行在iOS模擬器
flutter run -d ios

# 構建iOS應用
flutter build ios --release
```

#### **Web** (測試用)
```bash
# 運行在Chrome
flutter run -d chrome

# 構建Web版本
flutter build web --release
```

### � 自動化腳本說明

項目提供了四個Windows批處理腳本來簡化開發流程：

#### **1. run-android.bat** ⭐ **推薦**
- **功能**: 簡潔高效的一鍵運行腳本
- **特點**: 純英文界面，避免編碼問題，支持雙擊運行
- **適用**: 日常開發的最佳選擇
- **修復**: ✅ 支持雙擊直接運行，✅ 支持 `.\bat\run-android.bat` 命令

#### **2. build-and-run-android-simple.bat**
- **功能**: 完整的自動化編譯和運行流程
- **特點**: 包含詳細的環境檢查和錯誤處理
- **適用**: 首次運行或需要完整檢查時使用
- **修復**: ✅ 已修復目錄切換問題

#### **3. quick-android-run.bat**
- **功能**: 快速編譯和運行
- **特點**: 簡化流程，適合日常開發
- **適用**: 環境已配置好，需要快速測試時使用
- **修復**: ✅ 已修復目錄切換問題

#### **4. build-and-run-android.bat**
- **功能**: 最完整版本，包含詳細日誌
- **特點**: 最詳細的環境檢查和錯誤處理
- **適用**: 需要詳細了解每個步驟或問題排查時使用
- **修復**: ✅ 已修復目錄切換問題

**使用建議**：
- 🌟 **推薦使用**: `run-android.bat` (最穩定，支持雙擊)
- 🔰 **初學者**: `build-and-run-android-simple.bat`
- 🚀 **日常開發**: `quick-android-run.bat`
- 🔧 **問題排查**: `build-and-run-android.bat`

### �🐛 故障排除

#### **常見問題**

1. **依賴衝突**
```bash
flutter clean
flutter pub get
```

2. **Android模擬器無法啟動**
```bash
# 檢查Android SDK路徑
flutter doctor -v

# 重新創建模擬器
flutter emulators --create --name test_emulator
```

3. **編譯錯誤**
```bash
# 清理構建緩存
flutter clean
cd android
./gradlew clean
cd ..
flutter pub get
```

4. **模擬器性能問題**
   - 確保電腦已啟用硬件加速 (Intel HAXM 或 AMD Hypervisor)
   - 在Android Studio中調整模擬器RAM和存儲設置

#### **權限設置**

**Android權限** (`android/app/src/main/AndroidManifest.xml`):
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.CAMERA" />
```

**iOS權限** (`ios/Runner/Info.plist`):
```xml
<key>NSCameraUsageDescription</key>
<string>This app needs camera access to select images</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>This app needs photo library access to select images</string>
```

## 📋 開發指南

### 代碼規範
- 使用4空格縮進
- 行長度限制88字符
- 變量和函數使用snake_case
- 類名使用PascalCase
- 優先使用f-string格式化

### 狀態管理
使用Provider模式管理應用狀態：
- 主題模式切換
- 底部導航索引
- 選中的圖片列表
- 當前提示詞

### 路由配置
使用GoRouter進行聲明式路由：
```dart
GoRoute(
  path: '/splash',
  builder: (context, state) => const SplashScreen(),
),
```

## 🔧 自定義配置

### 添加新的插件風格
在 `plugin_marketplace_home_screen.dart` 中的 `_plugins` 列表添加新項目：

```dart
PluginItem(
  name: 'New Style',
  author: '@creator',
  imageUrl: 'https://example.com/image.jpg',
  isFavorite: false,
),
```

### 修改主題顏色
在 `app.dart` 中修改 `seedColor`：

```dart
colorScheme: ColorScheme.fromSeed(
  seedColor: const Color(0xFF7B61FF), // 修改這裡
),
```

## 📄 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情。

## 🤝 貢獻

歡迎提交 Pull Request 和 Issue！

## 📞 聯繫方式

如有問題或建議，請通過以下方式聯繫：
- 創建 GitHub Issue
- 發送郵件至開發團隊

---

**NanoBanana AI** - 讓AI圖像生成變得簡單而強大！ 🎨✨

一個基於Flutter的AI圖像生成應用程序，支持iOS和Android平台，採用iOS-first設計理念。

## 🚀 功能特色

### 核心功能
- **Splash Screen**: 帶有動畫效果的啟動畫面
- **Plugin Marketplace**: 插件市場主頁，展示各種AI生成風格
- **Multi-Image Input**: 多圖像輸入界面，支持參考圖片選擇和提示詞輸入
- **Membership**: 會員訂閱和積分購買系統
- **Settings**: 設置頁面，包含偏好設置、幫助和關於信息

### 技術特色
- **跨平台適配**: iOS和Android原生體驗
- **響應式設計**: 適配不同屏幕尺寸
- **狀態管理**: 使用Provider進行狀態管理
- **路由導航**: 使用GoRouter進行頁面導航
- **圖片選擇**: 支持從相冊選擇圖片

## 📱 應用流程

1. **啟動畫面** (`/splash`) - 應用啟動時的歡迎界面
2. **插件市場主頁** (`/home`) - 瀏覽和選擇AI生成風格
3. **圖像創建** (`/create`) - 選擇參考圖片並輸入提示詞
4. **會員中心** (`/membership`) - 管理訂閱和購買積分
5. **設置頁面** (`/settings`) - 應用設置和幫助信息

## 🏗️ 項目結構

```
lib/
├── main.dart                    # 應用入口
├── app.dart                     # 應用配置和路由
├── providers/
│   └── app_state_provider.dart  # 狀態管理
├── screens/
│   ├── splash_screen.dart       # 啟動畫面
│   ├── plugin_marketplace_home_screen.dart  # 主頁
│   ├── multi_image_input_screen.dart        # 圖像輸入
│   ├── membership_screen.dart   # 會員中心
│   └── settings_screen.dart     # 設置頁面
└── widgets/
    ├── adaptive_widgets.dart    # 適配性組件
    └── bottom_navigation.dart   # 底部導航
```

## 🛠️ 技術棧

### 核心依賴
- **Flutter SDK**: ^3.9.2
- **go_router**: ^14.6.2 - 路由管理
- **provider**: ^6.1.2 - 狀態管理
- **image_picker**: ^1.1.2 - 圖片選擇
- **cached_network_image**: ^3.4.1 - 網絡圖片緩存

### UI組件
- **flutter_staggered_grid_view**: ^0.7.0 - 瀑布流佈局
- **flutter_platform_widgets**: ^7.0.1 - 平台適配組件

## 🎨 設計理念

### iOS-First 適配策略
1. **導航欄**
   - iOS: CupertinoNavigationBar
   - Android: AppBar

2. **按鈕**
   - iOS: CupertinoButton
   - Android: ElevatedButton

3. **對話框**
   - iOS: CupertinoAlertDialog
   - Android: AlertDialog

4. **開關**
   - 統一使用 Switch.adaptive

### 顏色主題
- **主色調**: #7B61FF (紫色)
- **背景漸變**: #FDFCFB → #E2DFFF
- **強調色**: #4913EC (深紫色)

## 🚀 快速開始

### 環境要求
- Flutter SDK 3.9.2 或更高版本
- Dart SDK 3.0.0 或更高版本
- Android Studio / VS Code
- iOS開發需要Xcode (macOS)

### 安裝步驟

1. **克隆項目**
```bash
git clone <repository-url>
cd nano_banana
```

2. **安裝依賴**
```bash
flutter pub get
```

3. **運行應用**
```bash
# Android
flutter run

# iOS
flutter run -d ios

# Web (測試用)
flutter run -d chrome
```

### 構建發布版本
```bash
# Android APK
flutter build apk --release

# iOS
flutter build ios --release

# Web
flutter build web --release
```

## 📋 開發指南

### 代碼規範
- 使用4空格縮進
- 行長度限制88字符
- 變量和函數使用snake_case
- 類名使用PascalCase
- 優先使用f-string格式化

### 狀態管理
使用Provider模式管理應用狀態：
- 主題模式切換
- 底部導航索引
- 選中的圖片列表
- 當前提示詞

### 路由配置
使用GoRouter進行聲明式路由：
```dart
GoRoute(
  path: '/splash',
  builder: (context, state) => const SplashScreen(),
),
```

## 🔧 自定義配置

### 添加新的插件風格
在 `plugin_marketplace_home_screen.dart` 中的 `_plugins` 列表添加新項目：

```dart
PluginItem(
  name: 'New Style',
  author: '@creator',
  imageUrl: 'https://example.com/image.jpg',
  isFavorite: false,
),
```

### 修改主題顏色
在 `app.dart` 中修改 `seedColor`：

```dart
colorScheme: ColorScheme.fromSeed(
  seedColor: const Color(0xFF7B61FF), // 修改這裡
),
```

## 🐛 故障排除

### 常見問題

1. **依賴衝突**
```bash
flutter clean
flutter pub get
```

2. **iOS構建失敗**
```bash
cd ios
pod install
cd ..
flutter run
```

3. **圖片選擇權限**
確保在 `android/app/src/main/AndroidManifest.xml` 和 `ios/Runner/Info.plist` 中添加相應權限。

## 📄 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情。

## 🤝 貢獻

歡迎提交 Pull Request 和 Issue！

## 📞 聯繫方式

如有問題或建議，請通過以下方式聯繫：
- 創建 GitHub Issue
- 發送郵件至開發團隊

---

**NanoBanana AI** - 讓AI圖像生成變得簡單而強大！ 🎨✨
