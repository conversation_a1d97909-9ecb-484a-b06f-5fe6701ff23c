import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../widgets/bottom_navigation.dart';


class MembershipScreen extends StatefulWidget {
    const MembershipScreen({super.key});

    @override
    State<MembershipScreen> createState() => _MembershipScreenState();
}

class _MembershipScreenState extends State<MembershipScreen> {

    final List<MembershipPlan> _plans = [
        MembershipPlan(
            name: 'Free Plan',
            price: '\$0',
            period: '/ 7-day trial',
            features: [
                '3 images / day',
                'Upgrade required after trial',
            ],
            isPopular: false,
            buttonText: 'Start Free Trial',
            buttonColor: Colors.grey[900]!,
        ),
        MembershipPlan(
            name: 'Basic',
            price: '\$20',
            period: '/month',
            features: [
                '50 images / month',
            ],
            isPopular: false,
            buttonText: 'Choose Plan',
            buttonColor: Colors.grey[900]!,
        ),
        MembershipPlan(
            name: 'Pro',
            price: '\$50',
            period: '/month',
            features: [
                '150 images / month',
            ],
            isPopular: true,
            buttonText: 'Choose Plan',
            buttonColor: const Color(0xFF4913EC),
        ),
    ];

    final List<CreditPack> _creditPacks = [
        CreditPack(
            name: 'Starter Pack',
            credits: '60 Credits',
            price: '\$10',
        ),
        CreditPack(
            name: 'Standard Pack',
            credits: '170 Credits',
            price: '\$25',
        ),
        CreditPack(
            name: 'Pro Pack',
            credits: '370 Credits',
            price: '\$50',
        ),
    ];

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            backgroundColor: const Color(0xFFF7F5FF),
            body: SafeArea(
                child: Column(
                    children: [
                        _buildHeader(),
                        Expanded(
                            child: SingleChildScrollView(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                        _buildPlansSection(),
                                        const SizedBox(height: 32),
                                        _buildCreditsSection(),
                                        const SizedBox(height: 32),
                                        _buildBillingSection(),
                                        const SizedBox(height: 100), // Space for bottom nav
                                    ],
                                ),
                            ),
                        ),
                    ],
                ),
            ),
            bottomNavigationBar: Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                    return AdaptiveBottomNavigation(
                        currentIndex: 3,
                        onTap: appState.setBottomNavIndex,
                    );
                },
            ),
        );
    }

    Widget _buildHeader() {
        return Container(
            color: const Color(0xFFF7F5FF),
            child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                    children: [
                        IconButton(
                            onPressed: () => context.go('/home'),
                            icon: const Icon(
                                Icons.arrow_back_ios,
                                color: Color(0xFF110D1B),
                            ),
                        ),
                        const Expanded(
                            child: Text(
                                'Membership',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF110D1B),
                                ),
                            ),
                        ),
                        const SizedBox(width: 48), // Balance the back button
                    ],
                ),
            ),
        );
    }

    Widget _buildPlansSection() {
        return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                const Text(
                    'Choose Your Plan',
                    style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF110D1B),
                    ),
                ),
                const SizedBox(height: 16),
                ...(_plans.map((plan) => Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: _buildPlanCard(plan),
                ))),
            ],
        );
    }

    Widget _buildPlanCard(MembershipPlan plan) {
        return Container(
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: plan.isPopular 
                    ? Border.all(color: const Color(0xFF4913EC), width: 2)
                    : Border.all(color: Colors.grey[200]!),
                boxShadow: plan.isPopular 
                    ? [
                        BoxShadow(
                            color: const Color(0xFF4913EC).withOpacity(0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                        ),
                    ]
                    : [
                        BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                        ),
                    ],
            ),
            child: Stack(
                children: [
                    if (plan.isPopular)
                        Positioned(
                            top: 0,
                            right: 24,
                            child: Transform.translate(
                                offset: const Offset(0, -12),
                                child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                        color: const Color(0xFF4913EC),
                                        borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Text(
                                        'MOST POPULAR',
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                        ),
                                    ),
                                ),
                            ),
                        ),
                    Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                                Text(
                                    plan.name,
                                    style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF110D1B),
                                    ),
                                ),
                                const SizedBox(height: 8),
                                Row(
                                    crossAxisAlignment: CrossAxisAlignment.baseline,
                                    textBaseline: TextBaseline.alphabetic,
                                    children: [
                                        Text(
                                            plan.price,
                                            style: const TextStyle(
                                                fontSize: 32,
                                                fontWeight: FontWeight.w900,
                                                color: Color(0xFF110D1B),
                                            ),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                            plan.period,
                                            style: const TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w600,
                                                color: Colors.grey,
                                            ),
                                        ),
                                    ],
                                ),
                                const SizedBox(height: 16),
                                ...plan.features.map((feature) => Padding(
                                    padding: const EdgeInsets.only(bottom: 12.0),
                                    child: Row(
                                        children: [
                                            const Icon(
                                                Icons.check_circle,
                                                color: Color(0xFF4913EC),
                                                size: 20,
                                            ),
                                            const SizedBox(width: 12),
                                            Text(
                                                feature,
                                                style: const TextStyle(
                                                    fontSize: 16,
                                                    color: Colors.grey,
                                                ),
                                            ),
                                        ],
                                    ),
                                )),
                                const SizedBox(height: 8),
                                SizedBox(
                                    width: double.infinity,
                                    height: 48,
                                    child: ElevatedButton(
                                        onPressed: () {
                                            _selectPlan(plan.name);
                                        },
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor: plan.buttonColor,
                                            foregroundColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(12),
                                            ),
                                        ),
                                        child: Text(
                                            plan.buttonText,
                                            style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                            ),
                                        ),
                                    ),
                                ),
                            ],
                        ),
                    ),
                ],
            ),
        );
    }

    Widget _buildCreditsSection() {
        return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                const Text(
                    'Buy Credits',
                    style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF110D1B),
                    ),
                ),
                const SizedBox(height: 16),
                ...(_creditPacks.map((pack) => Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: _buildCreditPackCard(pack),
                ))),
            ],
        );
    }

    Widget _buildCreditPackCard(CreditPack pack) {
        return Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey[200]!),
                boxShadow: [
                    BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                    ),
                ],
            ),
            child: Row(
                children: [
                    Expanded(
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                                Text(
                                    pack.name,
                                    style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF110D1B),
                                    ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                    pack.credits,
                                    style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey,
                                    ),
                                ),
                            ],
                        ),
                    ),
                    ElevatedButton(
                        onPressed: () {
                            _purchaseCredits(pack);
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF4913EC),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 10,
                            ),
                        ),
                        child: Text(
                            pack.price,
                            style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                            ),
                        ),
                    ),
                ],
            ),
        );
    }

    Widget _buildBillingSection() {
        return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                const Text(
                    'Billing',
                    style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF110D1B),
                    ),
                ),
                const SizedBox(height: 16),
                _buildBillingItem('Payment History', Icons.arrow_forward_ios),
                const SizedBox(height: 8),
                _buildBillingItem('Manage Subscription', Icons.arrow_forward_ios),
            ],
        );
    }

    Widget _buildBillingItem(String title, IconData icon) {
        return Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey[200]!),
                boxShadow: [
                    BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                    ),
                ],
            ),
            child: Row(
                children: [
                    Expanded(
                        child: Text(
                            title,
                            style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF110D1B),
                            ),
                        ),
                    ),
                    Icon(
                        icon,
                        color: Colors.grey,
                        size: 16,
                    ),
                ],
            ),
        );
    }

    void _selectPlan(String planName) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text('Selected $planName'),
                backgroundColor: const Color(0xFF4913EC),
            ),
        );
    }

    void _purchaseCredits(CreditPack pack) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text('Purchasing ${pack.name}...'),
                backgroundColor: const Color(0xFF4913EC),
            ),
        );
    }
}

class MembershipPlan {
    final String name;
    final String price;
    final String period;
    final List<String> features;
    final bool isPopular;
    final String buttonText;
    final Color buttonColor;

    MembershipPlan({
        required this.name,
        required this.price,
        required this.period,
        required this.features,
        required this.isPopular,
        required this.buttonText,
        required this.buttonColor,
    });
}

class CreditPack {
    final String name;
    final String credits;
    final String price;

    CreditPack({
        required this.name,
        required this.credits,
        required this.price,
    });
}
