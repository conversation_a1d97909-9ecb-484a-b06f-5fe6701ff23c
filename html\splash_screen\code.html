<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter:wght@400;500;700&amp;family=Spline+Sans:wght@400;500;700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<title>AI Image Generation</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      @keyframes gradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }
      .gradient-bg {
        background: linear-gradient(-45deg, #FDE047, #FBBF24, #F59E0B, #D97706);
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
      }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-yellow-900" style='font-family: "Inter", "Spline Sans", sans-serif;'>
<div class="relative flex size-full min-h-screen flex-col justify-center items-center overflow-hidden p-4">
<div class="absolute inset-0 gradient-bg opacity-40"></div>
<div class="relative z-10 flex flex-col items-center justify-center text-center text-white space-y-8">
<div class="w-24 h-24 bg-white/20 rounded-3xl flex items-center justify-center backdrop-blur-sm">
<svg class="w-12 h-12 text-white" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<path d="M16.882 7.1182C16.882 10.9422 14.282 13.9872 12.0002 16.8822C9.7182 13.9872 7.1182 10.9422 7.1182 7.1182C7.1182 3.2942 10.9422 2.0002 12.0002 2.0002C13.0582 2.0002 16.882 3.2942 16.882 7.1182Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
<path d="M12 22C14.2091 22 16 20.2091 16 18C16 15.7909 14.2091 14 12 14C9.79086 14 8 15.7909 8 18C8 20.2091 9.79086 22 12 22Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</div>
<div>
<h1 class="text-4xl font-bold tracking-tight">NanoBanana AI</h1>
<p class="text-lg text-white/80 mt-2">Generate stunning visuals in seconds.</p>
</div>
</div>
</div>
</body></html>