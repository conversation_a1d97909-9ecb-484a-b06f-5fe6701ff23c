<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gray-50">
<div class="relative flex size-full min-h-screen flex-col bg-white" style='font-family: "Spline Sans", "Noto Sans", sans-serif;'>
<div class="relative w-full h-48">
<div class="absolute inset-0 bg-gradient-to-br from-purple-400 via-purple-500 to-indigo-500 opacity-20"></div>
<div class="relative z-10 p-4">
<div class="flex items-center justify-between pt-4">
<button class="text-slate-800 flex size-10 items-center justify-center rounded-full bg-white/50">
<span class="material-symbols-outlined"> arrow_back </span>
</button>
<h1 class="text-slate-900 text-xl font-bold">Settings</h1>
<div class="size-10"></div>
</div>
</div>
</div>
<div class="flex-1 -mt-32 px-4 space-y-6 pb-24">
<div class="bg-white rounded-2xl shadow-sm p-4">
<h2 class="text-slate-500 text-sm font-semibold px-2 pb-2">Preferences</h2>
<div class="space-y-1">
<a class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-100" href="#">
<div class="flex items-center gap-4">
<div class="flex items-center justify-center size-8 rounded-full bg-indigo-100 text-indigo-600">
<span class="material-symbols-outlined text-xl">language</span>
</div>
<p class="text-slate-800 text-base font-medium">Language</p>
</div>
<div class="flex items-center gap-2">
<p class="text-slate-500 text-sm">English</p>
<span class="material-symbols-outlined text-slate-400"> chevron_right </span>
</div>
</a>
<a class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-100" href="#">
<div class="flex items-center gap-4">
<div class="flex items-center justify-center size-8 rounded-full bg-indigo-100 text-indigo-600">
<span class="material-symbols-outlined text-xl">contrast</span>
</div>
<p class="text-slate-800 text-base font-medium">Theme</p>
</div>
<div class="flex items-center gap-2">
<p class="text-slate-500 text-sm">System</p>
<span class="material-symbols-outlined text-slate-400"> chevron_right </span>
</div>
</a>
<a class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-100" href="#">
<div class="flex items-center gap-4">
<div class="flex items-center justify-center size-8 rounded-full bg-indigo-100 text-indigo-600">
<span class="material-symbols-outlined text-xl">delete</span>
</div>
<p class="text-slate-800 text-base font-medium">Clear Cache</p>
</div>
<span class="material-symbols-outlined text-slate-400"> chevron_right </span>
</a>
</div>
</div>
<div class="bg-white rounded-2xl shadow-sm p-4">
<h2 class="text-slate-500 text-sm font-semibold px-2 pb-2">Help</h2>
<div class="space-y-1">
<a class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-100" href="#">
<div class="flex items-center gap-4">
<div class="flex items-center justify-center size-8 rounded-full bg-teal-100 text-teal-600">
<span class="material-symbols-outlined text-xl">lightbulb</span>
</div>
<p class="text-slate-800 text-base font-medium">Prompt Best Practices</p>
</div>
<span class="material-symbols-outlined text-slate-400"> chevron_right </span>
</a>
<a class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-100" href="#">
<div class="flex items-center gap-4">
<div class="flex items-center justify-center size-8 rounded-full bg-teal-100 text-teal-600">
<span class="material-symbols-outlined text-xl">support_agent</span>
</div>
<p class="text-slate-800 text-base font-medium">Customer Service</p>
</div>
<span class="material-symbols-outlined text-slate-400"> chevron_right </span>
</a>
</div>
</div>
<div class="bg-white rounded-2xl shadow-sm p-4">
<h2 class="text-slate-500 text-sm font-semibold px-2 pb-2">About</h2>
<div class="space-y-1">
<a class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-100" href="#">
<div class="flex items-center gap-4">
<div class="flex items-center justify-center size-8 rounded-full bg-gray-100 text-gray-600">
<span class="material-symbols-outlined text-xl">info</span>
</div>
<p class="text-slate-800 text-base font-medium">About this app</p>
</div>
<span class="material-symbols-outlined text-slate-400"> chevron_right </span>
</a>
</div>
</div>
</div>
<div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-[0_-1px_3px_rgba(0,0,0,0.05)]">
<div class="flex justify-around py-2">
<a class="flex flex-col items-center justify-end gap-1 text-slate-500" href="#">
<span class="material-symbols-outlined">home</span>
<p class="text-xs font-medium">Home</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-slate-500" href="#">
<span class="material-symbols-outlined">history</span>
<p class="text-xs font-medium">History</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-slate-500" href="#">
<span class="material-symbols-outlined">bookmark</span>
<p class="text-xs font-medium">Favorites</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-indigo-600" href="#">
<span class="material-symbols-outlined">person</span>
<p class="text-xs font-medium">Profile</p>
</a>
</div>
</div>
</div>

</body></html>