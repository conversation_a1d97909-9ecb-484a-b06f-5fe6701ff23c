import 'package:flutter/material.dart';

class AppStateProvider extends ChangeNotifier {
    ThemeMode _themeMode = ThemeMode.system;
    int _currentBottomNavIndex = 0;
    final List<String> _selectedImages = [];
    String _currentPrompt = '';

    // Getters
    ThemeMode get themeMode => _themeMode;
    int get currentBottomNavIndex => _currentBottomNavIndex;
    List<String> get selectedImages => _selectedImages;
    String get currentPrompt => _currentPrompt;

    // Theme management
    void setThemeMode(ThemeMode mode) {
        _themeMode = mode;
        notifyListeners();
    }

    void toggleTheme() {
        _themeMode = _themeMode == ThemeMode.light 
            ? ThemeMode.dark 
            : ThemeMode.light;
        notifyListeners();
    }

    // Bottom navigation
    void setBottomNavIndex(int index) {
        _currentBottomNavIndex = index;
        notifyListeners();
    }

    // Image selection
    void addSelectedImage(String imagePath) {
        if (!_selectedImages.contains(imagePath)) {
            _selectedImages.add(imagePath);
            notifyListeners();
        }
    }

    void removeSelectedImage(String imagePath) {
        _selectedImages.remove(imagePath);
        notifyListeners();
    }

    void clearSelectedImages() {
        _selectedImages.clear();
        notifyListeners();
    }

    // Prompt management
    void setPrompt(String prompt) {
        _currentPrompt = prompt;
        notifyListeners();
    }

    void clearPrompt() {
        _currentPrompt = '';
        notifyListeners();
    }
}
