<!DOCTYPE html>
<html><head>
<meta charset="utf-8"/>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-color: #4913ec;
      }
      .material-symbols-outlined {
        font-variation-settings:
        'FILL' 0,
        'wght' 400,
        'GRAD' 0,
        'opsz' 24
      }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-[#f7f5ff]">
<div class="relative flex size-full min-h-screen flex-col justify-between group/design-root overflow-x-hidden" style='font-family: Manrope, "Noto Sans", sans-serif;'>
<div class="flex flex-col">
<header class="flex items-center justify-between p-4 bg-[#f7f5ff]">
<button class="text-[#110d1b]">
<span class="material-symbols-outlined">arrow_back_ios_new</span>
</button>
<h1 class="text-[#110d1b] text-xl font-bold">Membership</h1>
<div class="w-8"></div>
</header>
<main class="p-4 space-y-8">
<section>
<div class="flex items-center justify-between mb-4">
<h2 class="text-2xl font-bold text-[#110d1b]">Choose Your Plan</h2>
</div>
<div class="space-y-4">
<div class="relative flex flex-col gap-4 rounded-2xl border border-gray-200 bg-white p-6 shadow-sm">
<div class="flex flex-col gap-1">
<h3 class="text-[#110d1b] text-lg font-bold">Free Plan</h3>
<p class="flex items-baseline gap-1 text-[#110d1b]">
<span class="text-4xl font-black tracking-tighter">$0</span>
<span class="text-sm font-semibold text-slate-500">/ 7-day trial</span>
</p>
</div>
<div class="flex flex-col gap-3 text-slate-600">
<div class="flex items-center gap-3">
<span class="material-symbols-outlined text-[var(--primary-color)]">check_circle</span>
<span>3 images / day</span>
</div>
<div class="flex items-center gap-3">
<span class="material-symbols-outlined text-[var(--primary-color)]">check_circle</span>
<span>Upgrade required after trial</span>
</div>
</div>
<button class="w-full h-12 mt-2 font-bold text-white rounded-xl bg-slate-900 hover:bg-slate-800">
                            Start Free Trial
                        </button>
</div>
<div class="relative flex flex-col gap-4 rounded-2xl border-2 border-gray-200 bg-white p-6 shadow-sm">
<div class="flex flex-col gap-1">
<h3 class="text-[#110d1b] text-lg font-bold">Basic</h3>
<p class="flex items-baseline gap-1 text-[#110d1b]">
<span class="text-4xl font-black tracking-tighter">$20</span>
<span class="text-sm font-semibold text-slate-500">/month</span>
</p>
</div>
<div class="flex flex-col gap-3 text-slate-600">
<div class="flex items-center gap-3">
<span class="material-symbols-outlined text-[var(--primary-color)]">check_circle</span>
<span>50 images / month</span>
</div>
</div>
<button class="w-full h-12 mt-2 font-bold text-white rounded-xl bg-slate-900 hover:bg-slate-800">
                            Choose Plan
                        </button>
</div>
<div class="relative flex flex-col gap-4 rounded-2xl border-2 border-[var(--primary-color)] bg-white p-6 shadow-lg">
<div class="absolute top-0 right-6 inline-block -translate-y-1/2 rounded-full bg-[var(--primary-color)] px-3 py-1 text-xs font-semibold text-white">MOST POPULAR</div>
<div class="flex flex-col gap-1">
<h3 class="text-[#110d1b] text-lg font-bold">Pro</h3>
<p class="flex items-baseline gap-1 text-[#110d1b]">
<span class="text-4xl font-black tracking-tighter">$50</span>
<span class="text-sm font-semibold text-slate-500">/month</span>
</p>
</div>
<div class="flex flex-col gap-3 text-slate-600">
<div class="flex items-center gap-3">
<span class="material-symbols-outlined text-[var(--primary-color)]">check_circle</span>
<span>150 images / month</span>
</div>
</div>
<button class="w-full h-12 mt-2 font-bold text-white rounded-xl bg-[var(--primary-color)] hover:bg-purple-700">
                            Choose Plan
                        </button>
</div>
</div>
</section>
<section>
<h2 class="text-2xl font-bold text-[#110d1b] mb-4">Buy Credits</h2>
<div class="space-y-4">
<div class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-2xl shadow-sm">
<div class="flex-grow">
<p class="text-lg font-bold text-[#110d1b]">Starter Pack</p>
<p class="text-sm text-slate-500">60 Credits</p>
</div>
<button class="flex items-center justify-center h-10 px-6 text-sm font-bold text-white rounded-xl bg-[var(--primary-color)] w-fit">
<span>$10</span>
</button>
</div>
<div class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-2xl shadow-sm">
<div class="flex-grow">
<p class="text-lg font-bold text-[#110d1b]">Standard Pack</p>
<p class="text-sm text-slate-500">170 Credits</p>
</div>
<button class="flex items-center justify-center h-10 px-6 text-sm font-bold text-white rounded-xl bg-[var(--primary-color)] w-fit">
<span>$25</span>
</button>
</div>
<div class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-2xl shadow-sm">
<div class="flex-grow">
<p class="text-lg font-bold text-[#110d1b]">Pro Pack</p>
<p class="text-sm text-slate-500">370 Credits</p>
</div>
<button class="flex items-center justify-center h-10 px-6 text-sm font-bold text-white rounded-xl bg-[var(--primary-color)] w-fit">
<span>$50</span>
</button>
</div>
</div>
</section>
<section>
<h2 class="text-2xl font-bold text-[#110d1b] mb-4">Billing</h2>
<div class="space-y-2">
<a class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-2xl shadow-sm" href="#">
<p class="font-medium text-[#110d1b]">Payment History</p>
<span class="material-symbols-outlined text-slate-400">arrow_forward_ios</span>
</a>
<a class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-2xl shadow-sm" href="#">
<p class="font-medium text-[#110d1b]">Manage Subscription</p>
<span class="material-symbols-outlined text-slate-400">arrow_forward_ios</span>
</a>
</div>
</section>
</main>
</div>
<footer class="sticky bottom-0 border-t border-gray-200 bg-white/80 backdrop-blur-lg">
<nav class="flex justify-around py-2">
<a class="flex flex-col items-center justify-end gap-1 text-slate-500" href="#">
<span class="material-symbols-outlined">home</span>
<p class="text-xs font-medium">Home</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-slate-500" href="#">
<span class="material-symbols-outlined">history</span>
<p class="text-xs font-medium">History</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-slate-500" href="#">
<span class="material-symbols-outlined">bookmark</span>
<p class="text-xs font-medium">Favorites</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[var(--primary-color)]" href="#">
<span class="material-symbols-outlined" style="font-variation-settings: 'FILL' 1">person</span>
<p class="text-xs font-medium">Profile</p>
</a>
</nav>
<div class="h-5 bg-white/80"></div>
</footer>
</div>

</body></html>