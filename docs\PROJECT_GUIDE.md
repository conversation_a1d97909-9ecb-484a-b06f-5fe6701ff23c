# 📖 Flutter iOS-First Cross-Platform Adaptive Project Document

## 1. Project Goal
- Build a **cross‑platform Flutter app** with an iOS‑first design philosophy while also supporting Android.  
- **UI style direction**:  
  - On **iOS**, prioritize Cupertino native look & feel (NavigationBar, Dialogs, Pickers).  
  - On **Android**, maintain Material Design consistency.  
- Ensure the app **looks native** on both platforms by leveraging Flutter’s **adaptive widgets**.

## 2. Project Structure
```
lib/
 ├─ main.dart              # Entry point
 ├─ app.dart               # Global setup (Theme, Routes)
 ├─ screens/
 │   ├─ home_screen.dart   # Main entry/home screen
 │   ├─ list_screen.dart   # List page with iOS swipe delete
 │   └─ dialog_screen.dart # Dialog showcase
 └─ widgets/
     └─ adaptive_app_bar.dart # Cross-platform AppBar widget
```

## 3. UI Design Rules
1. Navigation Bars  
   - iOS → CupertinoNavigationBar  
   - Android → AppBar  

2. Buttons  
   - iOS → CupertinoButton  
   - Android → ElevatedButton  
   - ✅ Alternatively, use PlatformButton from flutter_platform_widgets.

3. Dialogs  
   - iOS → CupertinoAlertDialog  
   - Android → AlertDialog  

4. Lists  
   - iOS → Support swipe-to-delete (flutter_slidable).  
   - Android → trailing arrow icons.  

5. Switches  
   - Use Switch.adaptive  

6. Bottom Sheets  
   - iOS → modal_bottom_sheet  
   - Android → showModalBottomSheet  

## 4. Packages to Use
- flutter_slidable  
- modal_bottom_sheet  
- cupertino_icons  
- flutter_platform_widgets (optional)  
- adaptive_dialog (optional)  

## 5. Functional Requirements
- HomeScreen: navigation buttons  
- ListScreen: ListView with adaptive delete  
- DialogScreen: adaptive dialogs  

## 6. Development Guidelines
- Prefer adaptive widgets  
- Use Platform.isIOS checks if needed  
- Keep clean structure  

## 7. Sample Adaptive Snippets

### Adaptive Button
```dart
return Platform.isIOS
  ? CupertinoButton(child: Text('Title'), onPressed: () {})
  : ElevatedButton(child: Text('Title'), onPressed: () {});
```

### Adaptive AlertDialog
```dart
return Platform.isIOS
  ? CupertinoAlertDialog(title: Text('Alert'))
  : AlertDialog(title: Text('Alert'));
```

## 8. Future Extensions
- Bottom Sheet enhancements  
- Tab Navigation  
- Date/Time Pickers  
- Dark Mode  
- Animations (Lottie/Rive)  
- i18n

## 9. Key Takeaways from Medium Article
- Adaptive Widgets ensure native look.  
- Use Platform.isIOS, Switch.adaptive, and ThemeData.adaptivePlatformDensity.  
- flutter_platform_widgets cleans up code.  

## ✅ Summary
The project merges **iOS-first rules** with **adaptive widgets**, ensuring native UX, clean code, and easy extensions.