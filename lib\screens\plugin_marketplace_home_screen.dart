import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../widgets/bottom_navigation.dart';
import '../data/plugin_data.dart';


class PluginMarketplaceHomeScreen extends StatefulWidget {
    const PluginMarketplaceHomeScreen({super.key});

    @override
    State<PluginMarketplaceHomeScreen> createState() => _PluginMarketplaceHomeScreenState();
}

class _PluginMarketplaceHomeScreenState extends State<PluginMarketplaceHomeScreen> {
    final TextEditingController _searchController = TextEditingController();
    String _selectedCategory = '';

    List<PluginItem> get _plugins {
        if (_selectedCategory.isEmpty) {
            return allPlugins;
        }

        switch (_selectedCategory) {
            case 'Collect':
                return allPlugins.where((plugin) => plugin.isFavorite).toList();
            case 'Generate':
                return allPlugins.where((plugin) => plugin.mode == 'Generate').toList();
            case 'Edit':
                return allPlugins.where((plugin) => plugin.mode == 'Edit').toList();
            default:
                return allPlugins;
        }
    }

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            body: Container(
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                            Color(0xFFFDFCFB),
                            Color(0xFFE2DFFF),
                        ],
                    ),
                ),
                child: SafeArea(
                    child: Column(
                        children: [
                            _buildHeader(),
                            _buildSearchBar(),
                            _buildCategoryTabs(),
                            Expanded(child: _buildPluginGrid()),
                        ],
                    ),
                ),
            ),
            bottomNavigationBar: Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                    return AdaptiveBottomNavigation(
                        currentIndex: 0,
                        onTap: appState.setBottomNavIndex,
                    );
                },
            ),
        );
    }

    Widget _buildHeader() {
        return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
                children: [
                    const Spacer(),
                    const Text(
                        'Plugins',
                        style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                        ),
                    ),
                    const Spacer(),
                    Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(20),
                        ),
                        child: IconButton(
                            onPressed: () {
                                // Add new plugin functionality
                            },
                            icon: const Icon(
                                Icons.add,
                                color: Colors.black,
                            ),
                        ),
                    ),
                ],
            ),
        );
    }

    Widget _buildSearchBar() {
        return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Container(
                height: 48,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                        BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                        ),
                    ],
                ),
                child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                        hintText: 'Search plugins...',
                        hintStyle: TextStyle(color: Colors.grey),
                        prefixIcon: Icon(Icons.search, color: Colors.grey),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                        ),
                    ),
                ),
            ),
        );
    }

    Widget _buildCategoryTabs() {
        final categories = ['Collect', 'Generate', 'Edit'];
        
        return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
                children: categories.map((category) {
                    final isSelected = category == _selectedCategory;
                    return Padding(
                        padding: const EdgeInsets.only(right: 12.0),
                        child: GestureDetector(
                            onTap: () {
                                setState(() {
                                    _selectedCategory = category;
                                });
                            },
                            child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 10,
                                ),
                                decoration: BoxDecoration(
                                    color: isSelected 
                                        ? const Color(0xFF7B61FF) 
                                        : Colors.white.withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                    category,
                                    style: TextStyle(
                                        color: isSelected ? Colors.white : Colors.black,
                                        fontWeight: isSelected 
                                            ? FontWeight.bold 
                                            : FontWeight.w500,
                                        fontSize: 14,
                                    ),
                                ),
                            ),
                        ),
                    );
                }).toList(),
            ),
        );
    }

    Widget _buildPluginGrid() {
        return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 0.85,
                ),
                itemCount: _plugins.length,
                itemBuilder: (context, index) {
                    return _buildPluginCard(_plugins[index]);
                },
            ),
        );
    }

    Widget _buildPluginCard(PluginItem plugin) {
        return GestureDetector(
            onTap: () {
                // Navigate to create screen with the plugin's prompt
                context.go('/create', extra: {'prompt': plugin.prompt});
            },
            child: Container(
                decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                        BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                        ),
                    ],
                ),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                        Expanded(
                            child: Stack(
                                children: [
                                    Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                            borderRadius: const BorderRadius.vertical(
                                                top: Radius.circular(16),
                                            ),
                                            image: DecorationImage(
                                                image: NetworkImage(plugin.imageUrl),
                                                fit: BoxFit.cover,
                                            ),
                                        ),
                                    ),
                                    Positioned(
                                        top: 8,
                                        right: 8,
                                        child: GestureDetector(
                                            onTap: () {
                                                setState(() {
                                                    plugin.isFavorite = !plugin.isFavorite;
                                                });
                                            },
                                            child: Container(
                                                width: 32,
                                                height: 32,
                                                decoration: BoxDecoration(
                                                    color: Colors.white.withOpacity(0.5),
                                                    borderRadius: BorderRadius.circular(16),
                                                ),
                                                child: Icon(
                                                    plugin.isFavorite 
                                                        ? Icons.favorite 
                                                        : Icons.favorite_border,
                                                    color: plugin.isFavorite 
                                                        ? Colors.red 
                                                        : Colors.grey[700],
                                                    size: 20,
                                                ),
                                            ),
                                        ),
                                    ),
                                ],
                            ),
                        ),
                        Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                    Text(
                                        plugin.name,
                                        style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black,
                                        ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                        plugin.author,
                                        style: const TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey,
                                        ),
                                    ),
                                    const SizedBox(height: 8),
                                    Align(
                                        alignment: Alignment.centerRight,
                                        child: Text(
                                            plugin.mode,
                                            style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500,
                                                color: const Color(0xFF7B61FF),
                                            ),
                                        ),
                                    ),
                                ],
                            ),
                        ),
                    ],
                ),
            ),
        );
    }
}


