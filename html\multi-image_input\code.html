<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-color: #3713ec;
      }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-[#f9f8fc]">
<div class="relative flex size-full min-h-screen flex-col justify-between group/design-root" style='font-family: "Spline Sans", "Noto Sans", sans-serif;'>
<div class="flex-grow">
<header class="sticky top-0 z-10 bg-[#f9f8fc]/80 backdrop-blur-sm">
<div class="flex items-center p-4">
<button class="text-[#100d1b]">
<span class="material-symbols-outlined">arrow_back_ios_new</span>
</button>
<h1 class="flex-1 text-center text-xl font-bold tracking-tight text-[#100d1b]">Create</h1>
<div class="w-8"></div>
</div>
<div class="absolute inset-x-0 bottom-0 h-24 bg-gradient-to-t from-purple-100/5 via-purple-100/10 to-transparent"></div>
</header>
<main class="px-4 pb-4">
<h2 class="text-xl font-bold tracking-tight text-[#100d1b]">Reference Images</h2>
<p class="mb-4 text-sm text-gray-500">Select the images you want to combine.</p>
<div class="grid grid-cols-2 gap-4">
<div class="group relative aspect-square cursor-pointer overflow-hidden rounded-2xl shadow-sm transition-all hover:shadow-lg">
<div class="absolute inset-0 z-10 rounded-2xl ring-4 ring-inset ring-transparent transition-all group-hover:ring-[var(--primary-color)]"></div>
<div class="absolute inset-0 bg-black/10 opacity-0 transition-opacity group-hover:opacity-100"></div>
<img alt="Reference Image 1" class="h-full w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBP5wDDTYR_nxulNtTroYPYSXzG3xd2Q5psybv6kzi5gBy-OLx5msjZ-xDeQxApC5RR7POSf1lnPn-Mp0K3xHbbtMcURRp33vS1cglrKzgnmVRLYfdZ2mywmTSBSvTaESH63GrYa2xQM0X0jMC1WSijB1ZjJwFskhEEGr-jy0BVPGLOY4bE4ZEw7ghIeiv0P5ajnEDo9tZB-RT_-2Sm7joOIoJnj32i9NZzbH3ATOUjSRO6c2PIybiRo0bzCsdH3DvbYO8tNqqph8_2"/>
</div>
<div class="group relative aspect-square cursor-pointer overflow-hidden rounded-2xl shadow-sm transition-all hover:shadow-lg">
<div class="absolute inset-0 z-10 rounded-2xl ring-4 ring-inset ring-transparent transition-all group-hover:ring-[var(--primary-color)]"></div>
<div class="absolute inset-0 bg-black/10 opacity-0 transition-opacity group-hover:opacity-100"></div>
<img alt="Reference Image 2" class="h-full w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAADeixopJqo_9K1VvB61rxPLCoD4ykNFVYunB_7y4Vgo2-pui5ssupmyTBfcmSmqz_NciulamxnWWyhrjW5iN_q2anfRZ8Zyvzu1v8JgWInY3m5hxhpGMqFqzDeWrg7MD37LGIPuJkiZwGTbm2lzaIxDEYMqh0wfKQAaKgObnTAglAQ_cLZbYq38GAJTFAea1G2bxXNcyPjnUWn0PLqygipPi0gunBEIXclqakoS8lXaE1rSj5QY1oMaP51d9xi7FXxtrxy0LXj0gR"/>
</div>
<div class="group relative aspect-square cursor-pointer overflow-hidden rounded-2xl shadow-sm transition-all hover:shadow-lg">
<div class="absolute inset-0 z-10 rounded-2xl ring-4 ring-inset ring-transparent transition-all group-hover:ring-[var(--primary-color)]"></div>
<div class="absolute inset-0 bg-black/10 opacity-0 transition-opacity group-hover:opacity-100"></div>
<img alt="Reference Image 3" class="h-full w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAo8-EbTPGZopC0wLDKfALWDgZzTonB2UaBVQOXhpWS3eRwB-yG1ytZxXHzQzEjI-Ck-7y2RK2Z_Scx5pQSYNIkwoaLti9TG7betVt-jEiEbow8ft5qcqHy-BpcNvt0cQ9zERtHmeAxp5Nwe7IXroz9Ph7H4yPLlQz1t-Ll5S-sgiD6_uSWcvjJfReM9QliXtzjw6zWeSF-k4PQYbgaOuZsHkOYJOFl_n_U3_QakmvGRnA1UbHxwQ8zc9QW2_rB93WHiQbdHYhHMfQS"/>
</div>
<div class="flex aspect-square cursor-pointer flex-col items-center justify-center rounded-2xl border-2 border-dashed border-gray-300 bg-gray-100 text-gray-400 transition-colors hover:border-[var(--primary-color)] hover:bg-purple-50 hover:text-[var(--primary-color)]">
<span class="material-symbols-outlined text-4xl">add_photo_alternate</span>
<span class="text-sm font-medium">Add Image</span>
</div>
</div>
<h2 class="mb-4 mt-8 text-xl font-bold tracking-tight text-[#100d1b]">Prompt</h2>
<div class="relative">
<textarea class="form-textarea w-full resize-none rounded-2xl border-none bg-white p-4 pr-12 text-base text-[#100d1b] shadow-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]" placeholder="Describe the image you want to create..." rows="4"></textarea>
<button class="absolute bottom-3 right-3 flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100 text-gray-500 transition-colors hover:bg-gray-200 hover:text-gray-700">
<span class="material-symbols-outlined">auto_awesome</span>
</button>
</div>
</main>
</div>
<footer class="sticky bottom-0">
<div class="px-4 pb-4 pt-2">
<button class="flex h-14 w-full items-center justify-center rounded-2xl bg-[var(--primary-color)] text-lg font-bold text-white shadow-lg shadow-purple-500/30 transition-transform hover:scale-105">
            Generate
          </button>
</div>
<nav class="border-t border-gray-200 bg-[#f9f8fc] px-4 pb-safe-bottom pt-2">
<div class="flex justify-around">
<a class="flex flex-1 flex-col items-center gap-1 text-[var(--primary-color)]" href="#">
<span class="material-symbols-outlined">home</span>
<span class="text-xs font-medium">Home</span>
</a>
<a class="flex flex-1 flex-col items-center gap-1 text-gray-500 hover:text-[var(--primary-color)]" href="#">
<span class="material-symbols-outlined">history</span>
<span class="text-xs font-medium">History</span>
</a>
<a class="flex flex-1 flex-col items-center gap-1 text-gray-500 hover:text-[var(--primary-color)]" href="#">
<span class="material-symbols-outlined">bookmark</span>
<span class="text-xs font-medium">Favorites</span>
</a>
<a class="flex flex-1 flex-col items-center gap-1 text-gray-500 hover:text-[var(--primary-color)]" href="#">
<span class="material-symbols-outlined">person</span>
<span class="text-xs font-medium">Profile</span>
</a>
</div>
</nav>
</footer>
</div>

</body></html>