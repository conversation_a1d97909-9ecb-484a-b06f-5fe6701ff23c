# Python Scripts 資料夾

這個資料夾包含用於處理 NanoBanana AI 應用程序插件數據的 Python 腳本。

## 📁 文件說明

### `generate_plugin_data.py`
**主要的插件數據轉換工具**

**功能**：
- 自動解析 `../samples/script_updated_en.txt` 文件
- 提取所有插件的完整信息（title, author, previewImage, prompt, mode）
- 將JavaScript格式的數據轉換為Flutter Dart格式
- 生成 `../lib/data/plugin_data.dart` 文件
- 自動處理字符串轉義和格式化

**使用方法**：
```bash
cd python_script
python generate_plugin_data.py
```

**輸出**：
- 在 `../lib/data/plugin_data.dart` 生成完整的插件數據文件
- 控制台顯示解析統計信息和驗證結果

### `verify_plugin_data.py`
**插件數據驗證工具**

**功能**：
- 驗證生成的Dart文件語法正確性
- 檢查插件數據完整性
- 統計插件數量和模式分布
- 對比原始文件和生成文件的數據一致性

**使用方法**：
```bash
cd python_script
python verify_plugin_data.py
```

### `test_plugin_count.py`
**插件數量測試工具**

**功能**：
- 快速檢查插件總數
- 驗證所有插件字段完整性
- 顯示前5個和後5個插件信息
- 統計Edit和Generate模式分布

**使用方法**：
```bash
cd python_script
python test_plugin_count.py
```

## 🎯 工作流程

1. **數據轉換**：運行 `generate_plugin_data.py` 將原始數據轉換為Dart格式
2. **數據驗證**：運行 `verify_plugin_data.py` 確保轉換結果正確
3. **快速測試**：運行 `test_plugin_count.py` 進行最終確認

## 📊 預期結果

- **插件總數**：105個（目標）
- **Edit模式**：約50個插件
- **Generate模式**：約55個插件
- **數據完整性**：100%的插件都有完整字段

## ⚠️ 注意事項

- 所有腳本都需要在 `python_script` 資料夾內執行
- 確保 `../samples/script_updated_en.txt` 文件存在
- 生成的文件會覆蓋現有的 `../lib/data/plugin_data.dart`
- 建議在運行前備份重要文件

## 🔧 技術特點

- **智能解析**：處理複雜的JavaScript對象格式
- **字符串轉義**：自動處理Dart字符串中的特殊字符
- **錯誤處理**：提供詳細的錯誤信息和建議
- **數據驗證**：多層驗證確保數據質量
