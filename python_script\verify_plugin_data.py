#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
插件數據驗證工具
驗證生成的 plugin_data.dart 文件的正確性
"""

import re
import os

def verify_dart_file():
    """驗證生成的Dart文件"""
    print("🔍 開始驗證 ../lib/data/plugin_data.dart...")
    
    dart_file = "../lib/data/plugin_data.dart"
    
    if not os.path.exists(dart_file):
        print(f"❌ 找不到文件: {dart_file}")
        return False
    
    try:
        with open(dart_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 無法讀取文件: {e}")
        return False
    
    # 統計插件數量
    plugin_items = re.findall(r'PluginItem\(', content)
    # 減去類定義中的一個
    plugin_count = len(plugin_items) - 1
    
    print(f"✅ 找到 {plugin_count} 個插件")
    
    # 檢查語法錯誤
    syntax_errors = []
    
    # 檢查未轉義的單引號（簡化檢查）
    problematic_quotes = re.findall(r"'[^']*'[^']*'[^']*'", content)
    if len(problematic_quotes) > 50:  # 允許一些正常的引號
        syntax_errors.append(f"可能存在未轉義的單引號")
    
    # 檢查未轉義的美元符號
    unescaped_dollars = re.findall(r"'[^']*\$[^{\\][^']*'", content)
    if unescaped_dollars:
        syntax_errors.append(f"存在未轉義的美元符號: {len(unescaped_dollars)} 處")
    
    # 檢查完整的插件結構
    complete_plugins = re.findall(
        r'PluginItem\(\s*name:\s*\'[^\']*\',\s*author:\s*\'[^\']*\',\s*imageUrl:\s*\'[^\']*\',\s*prompt:\s*\'[^\']*\',\s*mode:\s*\'[^\']*\',\s*isFavorite:\s*false,\s*\)',
        content,
        re.DOTALL
    )
    
    print(f"✅ 完整插件結構: {len(complete_plugins)} 個")
    
    # 統計模式分布
    edit_matches = re.findall(r"mode:\s*'Edit'", content)
    generate_matches = re.findall(r"mode:\s*'Generate'", content)
    
    print(f"✅ Edit模式: {len(edit_matches)} 個")
    print(f"✅ Generate模式: {len(generate_matches)} 個")
    
    # 檢查語法錯誤
    if syntax_errors:
        print("\n⚠️  發現潛在語法問題:")
        for error in syntax_errors:
            print(f"   {error}")
    else:
        print("\n✅ 沒有發現明顯的語法問題")
    
    return len(syntax_errors) == 0 and plugin_count > 0

def verify_original_file():
    """驗證原始文件中的插件數量"""
    print("\n🔍 驗證原始文件 ../samples/script_updated_en.txt...")
    
    original_file = "../samples/script_updated_en.txt"
    
    if not os.path.exists(original_file):
        print(f"❌ 找不到原始文件: {original_file}")
        return 0
    
    try:
        with open(original_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 無法讀取原始文件: {e}")
        return 0
    
    # 統計對象數量（更精確的方法）
    title_count = len(re.findall(r'title:\s*[\'"]', content))
    print(f"✅ 原始文件中找到 {title_count} 個title字段")
    
    # 統計完整的插件對象
    complete_objects = 0
    required_fields = ['title:', 'previewImage:', 'prompt:', 'author:', 'mode:']
    
    # 簡單統計包含所有必需字段的對象
    objects = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', content, re.DOTALL)
    for obj in objects:
        if all(field in obj for field in required_fields):
            complete_objects += 1
    
    print(f"✅ 有效插件對象: {complete_objects} 個")
    return complete_objects

def main():
    """主函數"""
    print("🚀 開始驗證插件數據...")
    
    # 驗證原始文件
    original_count = verify_original_file()
    
    # 驗證生成的Dart文件
    dart_valid = verify_dart_file()
    
    print(f"\n📊 驗證總結:")
    print(f"   原始文件插件數: {original_count}")
    
    if dart_valid:
        print("✅ Dart文件驗證通過")
    else:
        print("❌ Dart文件驗證失敗")
    
    print(f"\n🎯 建議:")
    if original_count >= 105:
        print(f"   原始文件有 {original_count} 個插件，已達到目標")
    elif original_count >= 100:
        print(f"   原始文件有 {original_count} 個插件，接近目標")
    else:
        print(f"   原始文件只有 {original_count} 個插件，可能需要檢查文件")
    
    print("   可以運行 'flutter run' 測試應用程序")
    print("   如需重新生成，請運行 'python generate_plugin_data.py'")

if __name__ == "__main__":
    main()
