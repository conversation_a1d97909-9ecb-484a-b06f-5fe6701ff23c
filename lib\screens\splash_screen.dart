import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class SplashScreen extends StatefulWidget {
    const SplashScreen({super.key});

    @override
    State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
    late AnimationController _animationController;
    late Animation<double> _fadeAnimation;
    late Animation<double> _scaleAnimation;

    @override
    void initState() {
        super.initState();
        _animationController = AnimationController(
            duration: const Duration(seconds: 2),
            vsync: this,
        );

        _fadeAnimation = Tween<double>(
            begin: 0.0,
            end: 1.0,
        ).animate(CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeInOut,
        ));

        _scaleAnimation = Tween<double>(
            begin: 0.8,
            end: 1.0,
        ).animate(CurvedAnimation(
            parent: _animationController,
            curve: Curves.elasticOut,
        ));

        _animationController.forward();

        // Navigate to home after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
            if (mounted) {
                context.go('/home');
            }
        });
    }

    @override
    void dispose() {
        _animationController.dispose();
        super.dispose();
    }

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            body: Container(
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                            Color(0xFFFDE047), // Yellow
                            Color(0xFFFBBF24), // Amber
                            Color(0xFFF59E0B), // Orange
                            Color(0xFFD97706), // Dark orange
                        ],
                        stops: [0.0, 0.33, 0.66, 1.0],
                    ),
                ),
                child: SafeArea(
                    child: Center(
                        child: AnimatedBuilder(
                            animation: _animationController,
                            builder: (context, child) {
                                return FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: ScaleTransition(
                                        scale: _scaleAnimation,
                                        child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                                // App Icon
                                                Container(
                                                    width: 96,
                                                    height: 96,
                                                    decoration: BoxDecoration(
                                                        color: Colors.white.withOpacity(0.2),
                                                        borderRadius: BorderRadius.circular(24),
                                                        boxShadow: [
                                                            BoxShadow(
                                                                color: Colors.black.withOpacity(0.1),
                                                                blurRadius: 20,
                                                                offset: const Offset(0, 10),
                                                            ),
                                                        ],
                                                    ),
                                                    child: const Icon(
                                                        Icons.auto_awesome,
                                                        size: 48,
                                                        color: Colors.white,
                                                    ),
                                                ),
                                                const SizedBox(height: 32),
                                                // App Title
                                                const Text(
                                                    'NanoBanana AI',
                                                    style: TextStyle(
                                                        fontSize: 32,
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.white,
                                                        letterSpacing: -0.5,
                                                    ),
                                                ),
                                                const SizedBox(height: 8),
                                                // Subtitle
                                                Text(
                                                    'Generate stunning visuals in seconds.',
                                                    style: TextStyle(
                                                        fontSize: 18,
                                                        color: Colors.white.withOpacity(0.8),
                                                        fontWeight: FontWeight.w500,
                                                    ),
                                                ),
                                                const SizedBox(height: 48),
                                                // Loading indicator
                                                SizedBox(
                                                    width: 32,
                                                    height: 32,
                                                    child: CircularProgressIndicator(
                                                        valueColor: AlwaysStoppedAnimation<Color>(
                                                            Colors.white.withOpacity(0.8),
                                                        ),
                                                        strokeWidth: 3,
                                                    ),
                                                ),
                                            ],
                                        ),
                                    ),
                                );
                            },
                        ),
                    ),
                ),
            ),
        );
    }
}
