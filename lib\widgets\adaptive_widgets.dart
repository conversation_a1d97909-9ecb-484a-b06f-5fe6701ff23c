import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:io';

class AdaptiveButton extends StatelessWidget {
    final String text;
    final VoidCallback? onPressed;
    final bool isPrimary;
    final EdgeInsetsGeometry? padding;

    const AdaptiveButton({
        super.key,
        required this.text,
        this.onPressed,
        this.isPrimary = true,
        this.padding,
    });

    @override
    Widget build(BuildContext context) {
        if (Platform.isIOS) {
            return CupertinoButton(
                onPressed: onPressed,
                padding: padding ?? const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                ),
                color: isPrimary ? const Color(0xFF7B61FF) : null,
                borderRadius: BorderRadius.circular(16),
                child: Text(
                    text,
                    style: TextStyle(
                        color: isPrimary ? Colors.white : const Color(0xFF7B61FF),
                        fontWeight: FontWeight.w600,
                    ),
                ),
            );
        } else {
            return ElevatedButton(
                onPressed: onPressed,
                style: ElevatedButton.styleFrom(
                    backgroundColor: isPrimary 
                        ? const Color(0xFF7B61FF) 
                        : Colors.transparent,
                    foregroundColor: isPrimary 
                        ? Colors.white 
                        : const Color(0xFF7B61FF),
                    elevation: isPrimary ? 2 : 0,
                    padding: padding ?? const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                        side: isPrimary 
                            ? BorderSide.none 
                            : const BorderSide(color: Color(0xFF7B61FF)),
                    ),
                ),
                child: Text(
                    text,
                    style: const TextStyle(fontWeight: FontWeight.w600),
                ),
            );
        }
    }
}

class AdaptiveAppBar extends StatelessWidget implements PreferredSizeWidget {
    final String title;
    final List<Widget>? actions;
    final Widget? leading;
    final bool automaticallyImplyLeading;

    const AdaptiveAppBar({
        super.key,
        required this.title,
        this.actions,
        this.leading,
        this.automaticallyImplyLeading = true,
    });

    @override
    Widget build(BuildContext context) {
        if (Platform.isIOS) {
            return CupertinoNavigationBar(
                middle: Text(
                    title,
                    style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                    ),
                ),
                leading: leading,
                trailing: actions != null && actions!.isNotEmpty 
                    ? Row(
                        mainAxisSize: MainAxisSize.min,
                        children: actions!,
                    )
                    : null,
                backgroundColor: Colors.transparent,
                border: null,
            );
        } else {
            return AppBar(
                title: Text(title),
                actions: actions,
                leading: leading,
                automaticallyImplyLeading: automaticallyImplyLeading,
                backgroundColor: Colors.transparent,
                elevation: 0,
            );
        }
    }

    @override
    Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class AdaptiveDialog extends StatelessWidget {
    final String title;
    final String content;
    final List<AdaptiveDialogAction> actions;

    const AdaptiveDialog({
        super.key,
        required this.title,
        required this.content,
        required this.actions,
    });

    @override
    Widget build(BuildContext context) {
        if (Platform.isIOS) {
            return CupertinoAlertDialog(
                title: Text(title),
                content: Text(content),
                actions: actions.map((action) => CupertinoDialogAction(
                    onPressed: action.onPressed,
                    isDefaultAction: action.isDefault,
                    isDestructiveAction: action.isDestructive,
                    child: Text(action.text),
                )).toList(),
            );
        } else {
            return AlertDialog(
                title: Text(title),
                content: Text(content),
                actions: actions.map((action) => TextButton(
                    onPressed: action.onPressed,
                    child: Text(
                        action.text,
                        style: TextStyle(
                            color: action.isDestructive 
                                ? Colors.red 
                                : null,
                        ),
                    ),
                )).toList(),
            );
        }
    }
}

class AdaptiveDialogAction {
    final String text;
    final VoidCallback? onPressed;
    final bool isDefault;
    final bool isDestructive;

    const AdaptiveDialogAction({
        required this.text,
        this.onPressed,
        this.isDefault = false,
        this.isDestructive = false,
    });
}

class AdaptiveSwitch extends StatelessWidget {
    final bool value;
    final ValueChanged<bool>? onChanged;

    const AdaptiveSwitch({
        super.key,
        required this.value,
        this.onChanged,
    });

    @override
    Widget build(BuildContext context) {
        return Switch.adaptive(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF7B61FF),
        );
    }
}
