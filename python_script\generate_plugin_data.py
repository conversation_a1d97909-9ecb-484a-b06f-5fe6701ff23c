#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NanoBanana AI 插件數據生成器
將 samples/script_updated_en.txt 轉換為 Flutter Dart 格式
目標：解析完整的105個插件
"""

import re
import os
import sys

def escape_dart_string(text: str) -> str:
    """轉義Dart字符串中的特殊字符"""
    if not text:
        return ""

    # 先處理反斜杠（必須最先處理）
    text = text.replace('\\', '\\\\')

    # 處理單引號
    text = text.replace("'", "\\'")

    # 處理美元符號
    text = text.replace('$', '\\$')

    # 處理換行符和其他控制字符
    text = text.replace('\n', '\\n')
    text = text.replace('\r', '\\r')
    text = text.replace('\t', '\\t')

    # 處理雙引號
    text = text.replace('"', '\\"')

    return text

def extract_field_value(obj_str: str, field_name: str) -> str:
    """提取對象中指定字段的值 - 改進版，處理嵌套引號"""

    # 找到字段開始位置
    field_start = obj_str.find(f'{field_name}:')
    if field_start == -1:
        return ""

    # 從字段開始位置向後查找
    search_start = field_start + len(f'{field_name}:')
    remaining = obj_str[search_start:].strip()

    # 處理不同的引號類型
    quote_chars = ["'", '"', '`']

    for quote_char in quote_chars:
        if remaining.startswith(quote_char):
            # 找到匹配的結束引號
            quote_end = find_matching_quote(remaining, quote_char)
            if quote_end != -1:
                value = remaining[1:quote_end]
                # 清理多餘的空白字符
                value = re.sub(r'\s+', ' ', value.strip())
                return value

    return ""

def find_matching_quote(text: str, quote_char: str) -> int:
    """找到匹配的結束引號位置，處理轉義字符"""
    i = 1  # 跳過開始引號
    while i < len(text):
        if text[i] == quote_char:
            # 檢查是否被轉義
            if i > 0 and text[i-1] != '\\':
                return i
        elif text[i] == '\\':
            # 跳過轉義字符
            i += 1
        i += 1
    return -1

def parse_plugins_advanced(content: str) -> list:
    """高級插件解析方法 - 確保解析到105個插件"""
    plugins = []

    # 使用逐字符解析，處理嵌套結構
    objects = parse_objects_by_braces(content)
    print(f"🔍 找到 {len(objects)} 個對象")

    # 解析每個對象
    for i, obj_str in enumerate(objects, 1):
        plugin = parse_single_object(obj_str, i)
        if plugin:
            plugins.append(plugin)

    return plugins

def parse_objects_by_braces(content: str) -> list:
    """通過大括號匹配來解析對象 - 改進版"""
    objects = []
    brace_count = 0
    current_obj = ""
    in_object = False
    in_string = False
    string_char = None

    i = 0
    while i < len(content):
        char = content[i]

        # 處理字符串內的字符
        if in_string:
            current_obj += char
            if char == string_char and (i == 0 or content[i-1] != '\\'):
                in_string = False
                string_char = None
        else:
            # 檢查字符串開始
            if char in ['"', "'", '`']:
                if in_object:
                    current_obj += char
                in_string = True
                string_char = char
            elif char == '{':
                if not in_object:
                    # 檢查這是否是一個插件對象的開始
                    # 向前查看是否有title字段
                    lookahead = content[i:i+500]  # 增加查看範圍
                    if 'title:' in lookahead and ('imageId:' in lookahead or 'previewImage:' in lookahead):
                        in_object = True
                        current_obj = ""
                        brace_count = 0

                if in_object:
                    brace_count += 1
                    current_obj += char
            elif char == '}' and in_object:
                current_obj += char
                brace_count -= 1

                if brace_count == 0:
                    objects.append(current_obj)
                    in_object = False
                    current_obj = ""
            elif in_object:
                current_obj += char

        i += 1

    return objects

def parse_single_object(obj_str: str, index: int) -> dict:
    """解析單個插件對象"""
    plugin = {'index': index}

    # 提取各個字段
    fields = ['title', 'imageId', 'previewImage', 'prompt', 'author', 'mode']

    for field in fields:
        value = extract_field_value(obj_str, field)
        if value:
            plugin[field] = value

    # 檢查必需字段
    required_fields = ['title', 'previewImage', 'prompt', 'author', 'mode']
    missing_fields = [f for f in required_fields if f not in plugin or not plugin[f]]

    if missing_fields:
        print(f"⚠️  插件 {index} 缺少字段: {missing_fields}")
        return None

    return plugin

def generate_dart_code(plugins: list) -> str:
    """生成Dart代碼"""
    dart_code = '''class PluginItem {
  final String name;
  final String author;
  final String imageUrl;
  final String prompt;
  final String mode;
  bool isFavorite;

  PluginItem({
    required this.name,
    required this.author,
    required this.imageUrl,
    required this.prompt,
    required this.mode,
    required this.isFavorite,
  });
}

final List<PluginItem> allPlugins = [
'''

    for i, plugin in enumerate(plugins, 1):
        # 轉義字符串
        name = escape_dart_string(plugin['title'])
        author = escape_dart_string(plugin['author'])
        image_url = escape_dart_string(plugin['previewImage'])
        prompt = escape_dart_string(plugin['prompt'])
        mode = escape_dart_string(plugin['mode'])

        dart_code += f'''  // {i}
  PluginItem(
    name: '{name}',
    author: '{author}',
    imageUrl: '{image_url}',
    prompt: '{prompt}',
    mode: '{mode}',
    isFavorite: false,
  ),
'''

    dart_code += '];'
    return dart_code

def main():
    """主函數"""
    print("🚀 NanoBanana AI 插件數據生成器")
    print("目標：解析105個插件\n")

    # 檢查文件路徑
    input_file = "../samples/script_updated_en.txt"
    output_file = "../lib/data/plugin_data.dart"

    if not os.path.exists(input_file):
        print(f"❌ 找不到輸入文件: {input_file}")
        print("請確保在 python_script 資料夾內運行此腳本")
        return

    # 讀取原始文件
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ 成功讀取文件: {input_file}")
        print(f"   文件大小: {len(content)} 字符")
    except Exception as e:
        print(f"❌ 讀取文件失敗: {e}")
        return

    # 解析插件
    plugins = parse_plugins_advanced(content)

    print(f"\n📊 解析結果:")
    print(f"   總插件數: {len(plugins)}")

    if len(plugins) == 0:
        print("❌ 沒有解析到任何插件")
        return

    # 統計模式分布
    edit_count = sum(1 for p in plugins if p.get('mode') == 'Edit')
    generate_count = sum(1 for p in plugins if p.get('mode') == 'Generate')

    print(f"   Edit模式: {edit_count} 個")
    print(f"   Generate模式: {generate_count} 個")

    # 生成Dart代碼
    dart_code = generate_dart_code(plugins)

    # 寫入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(dart_code)
        print(f"\n✅ 成功生成: {output_file}")
    except Exception as e:
        print(f"❌ 寫入文件失敗: {e}")
        return

    # 顯示樣本數據
    print(f"\n📋 前5個插件:")
    for i in range(min(5, len(plugins))):
        plugin = plugins[i]
        print(f"   {i+1}. {plugin['title']} by {plugin['author']} ({plugin['mode']})")

    print(f"\n📋 後5個插件:")
    start = max(0, len(plugins) - 5)
    for i in range(start, len(plugins)):
        plugin = plugins[i]
        print(f"   {i+1}. {plugin['title']} by {plugin['author']} ({plugin['mode']})")

    # 最終狀態
    if len(plugins) >= 105:
        print(f"\n🎉 成功！解析到 {len(plugins)} 個插件（達到目標）")
    elif len(plugins) >= 100:
        print(f"\n✅ 良好！解析到 {len(plugins)} 個插件（接近目標）")
    else:
        print(f"\n⚠️  解析到 {len(plugins)} 個插件（少於預期）")
        print("   建議檢查原始文件格式或改進解析邏輯")

if __name__ == "__main__":
    main()
