import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../providers/app_state_provider.dart';
import '../widgets/bottom_navigation.dart';

class MultiImageInputScreen extends StatefulWidget {
    final String? initialPrompt;

    const MultiImageInputScreen({super.key, this.initialPrompt});

    @override
    State<MultiImageInputScreen> createState() => _MultiImageInputScreenState();
}

class _MultiImageInputScreenState extends State<MultiImageInputScreen> {
    final TextEditingController _promptController = TextEditingController();
    final ImagePicker _picker = ImagePicker();
    List<String> _selectedImages = [];

    final List<String> _sampleImages = [
        'https://picsum.photos/400/400?random=10',
        'https://picsum.photos/400/400?random=11',
        'https://picsum.photos/400/400?random=12',
    ];

    @override
    void initState() {
        super.initState();
        final appState = Provider.of<AppStateProvider>(context, listen: false);
        // Use the passed prompt or fallback to the app state prompt
        _promptController.text = widget.initialPrompt ?? appState.currentPrompt;
        _selectedImages = List.from(appState.selectedImages);
    }

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            backgroundColor: const Color(0xFFF9F8FC),
            body: SafeArea(
                child: Column(
                    children: [
                        _buildHeader(),
                        Expanded(
                            child: SingleChildScrollView(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                        _buildReferenceImagesSection(),
                                        const SizedBox(height: 32),
                                        _buildPromptSection(),
                                        const SizedBox(height: 100), // Space for bottom button
                                    ],
                                ),
                            ),
                        ),
                    ],
                ),
            ),
            bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                    _buildGenerateButton(),
                    Consumer<AppStateProvider>(
                        builder: (context, appState, child) {
                            return AdaptiveBottomNavigation(
                                currentIndex: 2,
                                onTap: appState.setBottomNavIndex,
                            );
                        },
                    ),
                ],
            ),
        );
    }

    Widget _buildHeader() {
        return Container(
            color: const Color(0xFFF9F8FC),
            child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                    children: [
                        IconButton(
                            onPressed: () => context.go('/home'),
                            icon: const Icon(
                                Icons.arrow_back_ios,
                                color: Color(0xFF100D1B),
                            ),
                        ),
                        const Expanded(
                            child: Text(
                                'Create',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF100D1B),
                                ),
                            ),
                        ),
                        const SizedBox(width: 48), // Balance the back button
                    ],
                ),
            ),
        );
    }

    Widget _buildReferenceImagesSection() {
        return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                const Text(
                    'Reference Images',
                    style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF100D1B),
                    ),
                ),
                const SizedBox(height: 4),
                const Text(
                    'Select the images you want to combine.',
                    style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                    ),
                ),
                const SizedBox(height: 16),
                GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                        childAspectRatio: 1.0,
                    ),
                    itemCount: _sampleImages.length + 1,
                    itemBuilder: (context, index) {
                        if (index < _sampleImages.length) {
                            return _buildImageCard(_sampleImages[index], index);
                        } else {
                            return _buildAddImageCard();
                        }
                    },
                ),
            ],
        );
    }

    Widget _buildImageCard(String imageUrl, int index) {
        final isSelected = _selectedImages.contains(imageUrl);
        
        return GestureDetector(
            onTap: () {
                setState(() {
                    if (isSelected) {
                        _selectedImages.remove(imageUrl);
                    } else {
                        _selectedImages.add(imageUrl);
                    }
                });
                
                // Update app state
                final appState = Provider.of<AppStateProvider>(context, listen: false);
                if (isSelected) {
                    appState.removeSelectedImage(imageUrl);
                } else {
                    appState.addSelectedImage(imageUrl);
                }
            },
            child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                        BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                        ),
                    ],
                ),
                child: Stack(
                    children: [
                        Container(
                            width: double.infinity,
                            height: double.infinity,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                image: DecorationImage(
                                    image: NetworkImage(imageUrl),
                                    fit: BoxFit.cover,
                                ),
                            ),
                        ),
                        if (isSelected)
                            Container(
                                width: double.infinity,
                                height: double.infinity,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                        color: const Color(0xFF3713EC),
                                        width: 4,
                                    ),
                                    color: Colors.black.withOpacity(0.1),
                                ),
                            ),
                        if (isSelected)
                            const Positioned(
                                top: 8,
                                right: 8,
                                child: Icon(
                                    Icons.check_circle,
                                    color: Color(0xFF3713EC),
                                    size: 24,
                                ),
                            ),
                    ],
                ),
            ),
        );
    }

    Widget _buildAddImageCard() {
        return GestureDetector(
            onTap: _pickImage,
            child: Container(
                decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                        color: Colors.grey[300]!,
                        width: 2,
                        style: BorderStyle.solid,
                    ),
                ),
                child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                        Icon(
                            Icons.add_photo_alternate_outlined,
                            size: 48,
                            color: Colors.grey,
                        ),
                        SizedBox(height: 8),
                        Text(
                            'Add Image',
                            style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey,
                            ),
                        ),
                    ],
                ),
            ),
        );
    }

    Widget _buildPromptSection() {
        return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                const Text(
                    'Prompt',
                    style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF100D1B),
                    ),
                ),
                const SizedBox(height: 16),
                Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                            BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                            ),
                        ],
                    ),
                    child: Stack(
                        children: [
                            TextField(
                                controller: _promptController,
                                maxLines: 4,
                                decoration: const InputDecoration(
                                    hintText: 'Describe the image you want to create...',
                                    hintStyle: TextStyle(color: Colors.grey),
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.fromLTRB(16, 16, 48, 16),
                                ),
                                style: const TextStyle(
                                    fontSize: 16,
                                    color: Color(0xFF100D1B),
                                ),
                                onChanged: (value) {
                                    final appState = Provider.of<AppStateProvider>(
                                        context, 
                                        listen: false,
                                    );
                                    appState.setPrompt(value);
                                },
                            ),
                            Positioned(
                                bottom: 12,
                                right: 12,
                                child: Container(
                                    width: 32,
                                    height: 32,
                                    decoration: BoxDecoration(
                                        color: Colors.grey[100],
                                        borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: IconButton(
                                        onPressed: () {
                                            // AI suggestion functionality
                                        },
                                        icon: const Icon(
                                            Icons.auto_awesome,
                                            size: 16,
                                            color: Colors.grey,
                                        ),
                                    ),
                                ),
                            ),
                        ],
                    ),
                ),
            ],
        );
    }

    Widget _buildGenerateButton() {
        return Container(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                    onPressed: _selectedImages.isNotEmpty && _promptController.text.isNotEmpty
                        ? _generateImage
                        : null,
                    style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF3713EC),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 8,
                        shadowColor: const Color(0xFF3713EC).withOpacity(0.3),
                    ),
                    child: const Text(
                        'Generate',
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                        ),
                    ),
                ),
            ),
        );
    }

    Future<void> _pickImage() async {
        try {
            final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
            if (image != null) {
                setState(() {
                    _selectedImages.add(image.path);
                });
                
                final appState = Provider.of<AppStateProvider>(context, listen: false);
                appState.addSelectedImage(image.path);
            }
        } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error picking image: $e')),
            );
        }
    }

    void _generateImage() {
        // Show loading dialog
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const Center(
                child: CircularProgressIndicator(),
            ),
        );

        // Simulate generation process
        Future.delayed(const Duration(seconds: 3), () {
            Navigator.of(context).pop(); // Close loading dialog
            
            ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                    content: Text('Image generated successfully!'),
                    backgroundColor: Colors.green,
                ),
            );
        });
    }
}
