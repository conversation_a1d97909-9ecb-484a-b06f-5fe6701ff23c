class PluginItem {
  final String name;
  final String author;
  final String imageUrl;
  final String prompt;
  final String mode;
  bool isFavorite;

  PluginItem({
    required this.name,
    required this.author,
    required this.imageUrl,
    required this.prompt,
    required this.mode,
    required this.isFavorite,
  });
}

final List<PluginItem> allPlugins = [
  // 1
  PluginItem(
    name: 'Separate 3D Model',
    author: '@Zieeett',
    imageUrl: 'https://github.com/PicoTrex/Awesome-Nano-Banana-images/blob/main/images/case4/output.jpg?raw=true',
    prompt: 'Create the image in daylight and isometric view only [architecture]',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 2
  PluginItem(
    name: 'One-Click Smart Photo Enhancement',
    author: '@op7418',
    imageUrl: 'https://github.com/PicoTrex/Awesome-Nano-Banana-images/blob/main/images/case7/output.jpg?raw=true',
    prompt: 'This photo is boring and bland. Enhance it! Increase contrast, boost colors, improve lighting to make it richer, you can crop and remove details that affect the composition',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 3
  PluginItem(
    name: 'Switch to Overhead View',
    author: '@op7418',
    imageUrl: 'https://github.com/PicoTrex/Awesome-Nano-Banana-images/blob/main/images/case9/output.jpg?raw=true',
    prompt: 'Convert the photo to an overhead view and mark the photographer\\\'s position',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 4
  PluginItem(
    name: 'Anime Expressions',
    author: '@Gorden_Sun',
    imageUrl: 'https://i.mji.rip/2025/09/04/efc060e59e2d9c2e4a137db8564fc492.png',
    prompt: 'Character emotions sheet, multiple expressions of the provided character, featuring happy, sad, angry, surprised, shy, confused, playful, disgusted, thoughtful, crying, and embarrassed. Full set of emotions, clear and distinct expressions, clean background',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 5
  PluginItem(
    name: 'Batch Hairstyle Change',
    author: '@balconychy',
    imageUrl: 'https://github.com/PicoTrex/Awesome-Nano-Banana-images/blob/main/images/case15/output.jpg?raw=true',
    prompt: 'Generate portraits of this person with different hairstyles in a nine-grid format',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 6
  PluginItem(
    name: 'Outfit Change',
    author: '@skirano',
    imageUrl: 'https://i.mji.rip/2025/09/04/b9c7402974fba6627ab1b0bf3fce065d.png',
    prompt: 'Replace the clothing of the person in the input image with the target clothing shown in the reference image. Keep the person\'s pose, facial expression, background, and overall realism unchanged. Make the new clothing look natural, well-fitted, and consistent with lighting and shadows. Don\'t change the person\'s identity or environment—only change the clothes',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 7
  PluginItem(
    name: 'Hairstyle Change',
    author: 'Official',
    imageUrl: 'https://i.mji.rip/2025/09/04/c4dffca8a2916cd1fbefa21237751b81.png',
    prompt: 'Please carefully analyze the photo I provided. Your task is to change the hairstyle of the main person in the photo to a new one, while strictly following these rules: 1. **Identity Preservation**: Must completely preserve the person\'s facial features, facial structure, skin texture, and expression to ensure it looks like the same person. 2. **Background Unchanged**: The background, environment, and lighting conditions where the person is located must remain unchanged. 3. **Body Posture Unchanged**: The person\'s head posture, body pose, and clothing must remain unchanged. 4. **Seamless Integration**: The new hairstyle needs to be intelligently adjusted according to the person\'s head shape, face shape, and on-site lighting to ensure that the hair texture, luster, and shadows perfectly blend with the original photo, achieving a highly realistic and seamless effect. --- **Women\'s Hairstyle References:** * Flowing long straight hair * Romantic wavy curls * Playful short bob * Elegant French bangs with shoulder-length hair * Exquisite vintage updo * Chic and neat pixie cut * Fluffy afro curls * High ponytail * Dreadlocks * Silver-grey ombre long hair **Men\'s Hairstyle References:** * Classic business slick-back * Modern textured short hair / Quiff * Clean buzz cut * Retro middle part hairstyle * Fluffy Korean-style curly hair * Casual shoulder-length long hair * Undercut (sides shaved short, top left long) * Mohawk * Man bun --- Please change the person\'s hairstyle to: Playful short bob',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 8
  PluginItem(
    name: 'Time Filter',
    author: 'Official',
    imageUrl: 'https://i.mji.rip/2025/09/04/281360a8257436f6ad0b5e56b0982deb.png',
    prompt: 'Please reimagine the person in the photo to completely match the style of a specific era. This includes the person\'s clothing, hairstyle, the overall image quality and filters and composition of the photo, as well as the overall aesthetic style unique to that era. The final output must be a highly realistic image that clearly shows the person. Target era: 1900',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 9
  PluginItem(
    name: 'Black and White Artistic Portrait',
    author: 'LinuxDO@Bensong',
    imageUrl: 'https://i.mji.rip/2025/09/04/f03851e1fbea897dee75a109d497e2c7.png',
    prompt: 'High-resolution black and white portrait photography work, using editorial and artistic photography style. Keep the person\'s facial features consistent, only changing posture and composition. Background is a soft gradient from medium gray to near pure white, with delicate film grain texture, creating the atmosphere of classic black and white imagery. The subject wears a black T-shirt, appearing in different random poses: hand touching face, fingers interlaced across chest, partially covering face with hand, lightly touching chin, etc., emphasizing natural, elegant hand movements. The face still retains the original expression, only showing changes in angle and lighting, capturing details of eyes, cheekbones, or lip corners. Lighting is gentle directional light, softly outlining the texture of face, hands, and T-shirt; the composition is simple with large areas of negative space. No text or logos, only light, shadow, posture, and emotion intertwined. The overall atmosphere is intimate and eternal, like a pause between breathing or contemplation, captured as a poetic moment.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 10
  /*PluginItem(
    name: '3D Model Figure (Complex Background)',
    author: 'LinuxDO@DT2025',
    imageUrl: 'https://i.mji.rip/2025/09/04/a5fb782fded5b3778e2b39a455aa1fad.png',
    prompt: 'Generate a high-quality, photorealistic 3D model figure image from the user-provided 2D image. The figure should be expertly crafted, meticulously capturing the essence and design of the 2D character illustration. **Figure Details:** - **Pose:** The figure\'s pose should be dynamic, reflecting the original character\'s action or iconic posture from the 2D image. - **Clothing & Accessories:** Faithfully reproduce all clothing, armor, accessories (such as weapons, jewelry, headwear) and complex patterns of the 2D character in three-dimensional form, with realistic textures (such as fabric wrinkles, metallic luster, leather texture). - **Hair:** Hair should be sculpted with flowing and realistic styling that matches the character\'s hairstyle and length. - **Facial Features:** The figure\'s face should accurately present the character\'s expression and facial features. - **Material Appearance:** The figure should present the texture of a well-made plastic or resin model, with subtle reflections and appropriate material luster. **Scene & Display:** - **Environment:** The figure is placed on a clean and organized desktop, using a circular transparent acrylic base, like a model enthusiast\'s studio. - **Background Screen:** In the background behind the figure, there is a computer monitor displaying the 3D model Blender modeling process of this character, showing its digital model. The screen should emit a soft glow, illuminating part of the desktop. - **Packaging Box:** Next to the figure, slightly tilted toward the viewer, is the figure\'s retail packaging box. The packaging box\'s art design should feature the same character in a similar pose to the figure, with stylized brand names (such as \"Good Smile Company\" or similar fictional brands), possibly with character names. The packaging box should look professionally designed. - **Desktop Items:** Around the figure and packaging box on the desktop, various model tools and supplies are scattered, which can include: - Small paint cans or paint bottles - Fine brushes - Model tools (such as tweezers, small cutting knives, carving tools) - Cutting mat with grid lines - Reference books or albums - Other small art-related items that suggest a creative workspace. - **Lighting:** The scene should be lit with soft natural light or studio-like lighting, highlighting the figure\'s details and creating soft shadows for depth and realism. **Overall Aesthetics:** The image should convey the feeling of a completed, professionally made collectible figure, displayed in the context of its creation and development. The focus is on the figure itself, but the surrounding elements enhance the narrative of its creation and display.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 11
  PluginItem(
    name: 'Retro Propaganda Poster',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/d8ee52518aa3db45867fbaac63b4b57f6ad2e24e96a7519bab0c306747c0da21/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d74685a656a4d675830504752316e50796a315a33742e706e673f763d31',
    prompt: 'Retro propaganda poster style, highlighting Chinese text, background with red and yellow radial pattern. A beautiful young woman in the center of the image, drawn in exquisite retro style, with a smile, elegant temperament, and affinity. The theme is an advertisement for GPT\'s latest AI drawing service, emphasizing \'Amazing price 9.9/piece\', \'Suitable for various scenarios, image fusion, local redrawing\', \'Submit 3 modifications per piece\', \'AI direct output, no modification needed\', prominently marked at the bottom \'If interested, click \\\"I want it\\\" in the lower right\', with a finger clicking button action drawn in the lower right corner, and the OpenAI logo displayed in the lower left corner.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 12
  PluginItem(
    name: 'Custom Anime Figure',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/db8e5dc52a7c7d814c573877ee03225c4d4e761d0d987fbec05f1c8f3be8ebe2/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d71564b36666d2d66503861342d4c7870614e374a692e706e673f763d31',
    prompt: 'Generate a photo of an anime-style figure placed on a desktop, presented from a casual, relaxed perspective as if taken casually with a phone. The figure model is based on the person in the attached photo, accurately reproducing the full-body pose, facial expression, and clothing style of the person in the photo, ensuring the figure is presented in full body. The overall design is exquisite and delicate, with hair and clothing using natural and soft gradient colors and delicate textures, leaning towards Japanese anime style, rich in detail, realistic texture, and beautiful appearance.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 13
  PluginItem(
    name: 'Custom Q-Version Keychain',
    author: '@azed_ai',
    imageUrl: 'https://camo.githubusercontent.com/0749f414a01d6b6e053e86e0edd1877d1c7a5666683b04071da0115cf7830653/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d424f4877686d6b2d482d4c785133393865706b50702e706e673f763d31',
    prompt: 'A close-up photo showing a cute, colorful keychain being held by a human hand. The keychain is shaped like a Q-version style of [reference image]. The keychain is made of soft rubber material with thick black outlines, attached to a small silver key ring, with a neutral-toned background.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 14
  PluginItem(
    name: 'Golden Pendant Necklace',
    author: '@azed_ai',
    imageUrl: 'https://camo.githubusercontent.com/ef76535afd5a80239f3a4da844f5ffd07882b93ad2b27e8d12850061cb330a22/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d6a79516c7674765963795957753631585f63576b362e706e673f763d31',
    prompt: 'A photorealistic close-up image showing a gold pendant necklace held by a woman\\\'s hand. The pendant is engraved with a relief pattern of [image/emoji], hanging on a polished gold chain. The background is a soft, blurred neutral beige tone, using natural lighting, realistic skin tone, product photography style, 16:9 aspect ratio.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 15
  PluginItem(
    name: 'Original Pokémon Generation',
    author: '@Anima_Labs',
    imageUrl: 'https://camo.githubusercontent.com/c4a11a7e1012e9f7b1c0a9da081507a923ebc5d9f2a1f02e1b552a6f398d3060/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d306679596f3764325663337566486e5855306678682e706e673f763d31',
    prompt: 'Create an original creature based on this object (provided photo). The creature should look like it belongs to a fantasy monster-catching universe, with a cute or cool design influenced by retro Japanese RPG monster art. The image must include:\\\\n – A full-body view of the creature, inspired by the object\\\'s shape, material, or purpose.\\\\n – A small sphere or capsule at its feet (similar to a Poké Ball), with design patterns and colors that match the object\\\'s appearance—not a standard Poké Ball, but a custom design.\\\\n – An invented name for the creature, displayed next to or below it. – Its elemental type (e.g., fire, water, metal, nature, electric...), based on the object\\\'s core attributes. The illustration should look like it\\\'s from a fantasy creature encyclopedia, with clean lines, soft shadows, and expressive, character-driven design.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 16
  PluginItem(
    name: '3D Q-Version University Personification',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/a4ec79c77aa9d82a3ac05572963439535987464070ab3a0f18f05b6cf28a1484/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4a6e687479666157524c4a34387079314673324c382e706e673f763d31',
    prompt: 'Draw a personified 3D Q-version beautiful girl image for {Northwestern Polytechnical University}, embodying the school\\\'s {aviation, aerospace, and marine three-navigation} characteristics',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 17
  PluginItem(
    name: 'Logo-Shaped Creative Bookshelf',
    author: '@umesh_ai',
    imageUrl: 'https://camo.githubusercontent.com/8c9656afeca8088a32f1e33e896fcac050ca4a69dd854ced4df32b903727df74/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d6833675f616a7455356873373059793074736b694e2e706e673f763d31',
    prompt: 'Photograph a modern bookshelf whose design is inspired by the shape of [LOGO]. The bookshelf consists of smooth, interconnected curves forming multiple compartments of varying sizes. The overall material is smooth matte black metal with wooden shelves inside the curves. Soft warm LED strips outline the inner curve contours. The bookshelf is mounted on a neutral-toned wall, filled with colorful books, small green plants, and minimalist art pieces. The overall atmosphere is creative, elegant, and slightly futuristic.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 18
  PluginItem(
    name: 'Silhouette Art',
    author: '@umesh_ai',
    imageUrl: 'https://camo.githubusercontent.com/cb6e5f986b2031c8eb3953f29fa01733c18907ef1a2828e72674d9c28bbe5b2f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d576e46454552544a4a626e4470636a4b64623335552e706e673f763d31',
    prompt: 'A basic outline silhouette of an [Eastern Dragon]. Background is bright yellow, silhouette is solid black fill.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 19
  PluginItem(
    name: 'Frosted Glass Silhouette Contrast',
    author: '@umesh_ai',
    imageUrl: 'https://camo.githubusercontent.com/39b333bbe057c8bfbbec026f843b2cfb9d7a399ae63eef6121839731786ecb0c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d7044736142346f5f6f694e496a75645f6a357970702e706e673f763d31',
    prompt: 'A black and white photograph showing a blurred silhouette of a [subject] behind a frosted or translucent surface. Their [part] outline is clear, pressed against the surface, contrasting sharply with the rest of their hazy, blurred figure. The background is a soft gray gradient, enhancing the mysterious and artistic atmosphere.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 20
  PluginItem(
    name: 'Selfie-Generated Bobblehead',
    author: '@thisdudelikesAI',
    imageUrl: 'https://camo.githubusercontent.com/65210cc20d1ddd967e05e0cc20805dccceda04f3d30991e2e1925a8f86b54b1c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d654a7a7761703765374b7353774a6f665933696a382e706e673f763d31',
    prompt: 'Turn this photo into a bobblehead: head slightly enlarged, keeping face accurate, body cartoonized. [Place it on a bookshelf].',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 21
  PluginItem(
    name: 'Three Animals Landmark Selfie',
    author: '@berryxia_ai',
    imageUrl: 'https://camo.githubusercontent.com/fb16e65d547095227d221354766db4c0ee775c9d5247e6337fe1397f0ee42d3b/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d794164365071514d7438365658364e68315146436c2e706e673f763d31',
    prompt: 'A close-up selfie of three [animal types] in front of the iconic [landmark], each with different expressions, shot during golden hour with cinematic lighting. The animals are close to the camera, heads together, mimicking selfie poses, showing expressions of joy, surprise, and calm. The background shows the complete architectural details of the [landmark], with soft lighting and warm atmosphere. Shot in photographic, realistic cartoon style, high detail, 1:1 aspect ratio.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 22
  PluginItem(
    name: 'Perspective 3D Pop-out Effect',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/ff60c97d59ac8dcf08130db0dc8dc94f22222cee56f80800d4950e53170facd6/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d5a775834584a427a3542714d63764f585963656e302e706e673f763d31',
    prompt: 'Ultra-realistic, shot from above looking down, a beautiful Instagram model [Anne Hathaway / see reference image], with exquisite beautiful makeup and fashionable styling, standing on a smartphone screen held by someone, creating a strong perspective illusion. Emphasize the three-dimensional effect of the girl standing out from the phone. She wears black-framed glasses, dressed in street style, playfully posing cutely. The phone screen is processed as a dark floor, like a small stage. The scene uses strong forced perspective to show the proportion difference between palm, phone, and girl. Background is clean gray, using soft indoor lighting, shallow depth of field, overall style is surreal realistic composite. Particularly strong perspective',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 23
  PluginItem(
    name: 'Google Maps to Ancient Treasure Map',
    author: '@umesh_ai',
    imageUrl: 'https://camo.githubusercontent.com/2b53ef31557db7f68efd49a9b95e6cf04e40863e7bfc5878ab654ec7056283fa/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d6a5058363570464c424d54767a56614367536a6c7a2e706e673f763d31',
    prompt: 'Transform the image into an ancient treasure map drawn on old parchment. The map contains detailed elements such as sailing ships on the ocean, ancient ports or castles on coastlines, dotted paths leading to a large \"X\" marking the treasure location, mountains, palm trees, and a decorative compass rose. The overall style is reminiscent of old pirate adventure movies.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 24
  PluginItem(
    name: 'Branded Keyboard Keycaps',
    author: '@egeberkina',
    imageUrl: 'https://camo.githubusercontent.com/08cd326bf7298cec0e74668e927df19988dd715cbe5968a82583554bf8a8fd1b/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d712d35377a6149545362376435593231596136526b2e706e673f763d31',
    prompt: 'An ultra-realistic 3D render showing four mechanical keyboard keycaps arranged in a tight 2x2 grid, all keycaps touching each other. Viewed from an isometric angle. One keycap is transparent with \"{just}\" printed in red. The other three keycaps use colors: {black, purple, and white}. One keycap has the Github logo. The other two keycaps have \"{fork}\" and \"{it}\" written on them respectively. Realistic plastic texture, rounded sculpted keycaps, soft shadows, clean light gray background.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 25
  PluginItem(
    name: 'Miniature Tilt-Shift Photography',
    author: '@terry623',
    imageUrl: 'https://camo.githubusercontent.com/e10a8da63bb593ebe441a539072aee82de9a2a03dea0420002133fe0a23980eb/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d64346d7443793244755a6b32626b5869444d6f4f532e706e673f763d31',
    prompt: 'Ultra-high detail miniature [Cyberpunk] landscape viewed from above, using tilt-shift lens effects. The scene is filled with toy-like elements, all rendered in high-resolution CG. Dramatic lighting creates a cinematic atmosphere with vivid colors and strong contrast, emphasizing depth of field effects and realistic micro perspective, making viewers feel like they\\\'re looking down at a toy world-like miniature reality. The image contains numerous visual gags and details with high replay value',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 26
  PluginItem(
    name: 'Chrome Emoji Badge',
    author: '@egeberkina',
    imageUrl: 'https://camo.githubusercontent.com/4a973f06b0fc116ae1f42b5a5ec370f08ae0606212cd5f5e1b6e2fd5e1724b06/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d2d51577a495941744f374b433348746868736a48302e706e673f763d31',
    prompt: 'High-precision 3D render showing a metallic badge according to emoji icon {👍}, mounted on a vertical product card, with ultra-smooth chrome texture and rounded 3D icon styling, stylized futuristic design with soft reflections and clean shadows. The paper card has a punched European-style hanging hole at the top center, with a prominent title \"{Awesome}\" above the badge and a fun slogan \"{Smash that ⭐ if you like it!}\" below. Background is soft gray, using soft studio lighting, overall minimalist style.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 27
  PluginItem(
    name: 'Children\\\'s Coloring Page Illustration',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/77d141c0b20a88c50ae0f517d829f92e0743f32220e023b795eba354669d6167/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d76566f56516b367256514246326247554d47664f722e706e673f763d31',
    prompt: 'A black and white line drawing coloring illustration, suitable for direct printing on standard size (8.5x11 inches) paper, without paper borders. The overall illustration style is fresh and simple, using clear and smooth black outline lines, no shadows, no grayscale, no color fill, pure white background, convenient for coloring. [Also, to help users who don\'t know how to color, please generate a complete colored version as a small image in the lower right corner for reference] Target audience: [6-9 year old children] Scene description: [A unicorn walking on the grass in the forest, sunny day, blue sky and white clouds]',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 28
  PluginItem(
    name: 'Letter and Word Meaning Fusion',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/ab4ecc5f919ae1f59f23dd6dbc42cb6e75aaecc7369943cc4346a2958d2efbc7/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d646b335630734c4c54443966515730444d3970786e2e706e673f763d31',
    prompt: 'Integrate the meaning of the word into the letters, cleverly combining graphics and letters. Word: { beautify } Add a brief explanation of the word below',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 29
  PluginItem(
    name: 'Double Exposure',
    author: 'rezzycheck',
    imageUrl: 'https://camo.githubusercontent.com/6e87a6abd6bce57d9e0e9fa763e8172920e3a99769b32314adcbe60f1dccb5a4/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d7557417442794a353434636452356e426a4d4d33482e706e673f763d31',
    prompt: 'Double exposure, Midjourney style, fused, blended, overlaid double exposure image, double exposure style. An outstanding masterpiece by Yukisakura, showing a wonderful double exposure composition that harmoniously interweaves the silhouette of Aragorn, son of Arathorn, with the visually striking, rugged landscape of Middle-earth in vibrant spring. The scene of sun-drenched pine forests, mountain peaks, and a lone horse traversing the path echoes outward from the texture of his figure, adding layers of narrative and solitude. As the clean, distinct monochrome background maintains sharp contrast, wonderful tension gradually forms, drawing all focus to the richly layered double exposure. It features a vibrant full-color scheme within Aragorn\\\'s silhouette, and clear, deliberate lines that trace each contour with emotional precision. (Detailed:1.45). (Detailed background:1.4).',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 30
  PluginItem(
    name: 'Surreal Interactive Scene',
    author: '@umesh_ai',
    imageUrl: 'https://camo.githubusercontent.com/cd6872f458c49d960a9995a18d66d631fc7ca4c4725ffdde68c1ff3b631ee6b4/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d6e76414571617876736c446e2d703268434b62484e2e706e673f763d31',
    prompt: 'A pencil sketch depicting a scene where [Subject 1] interacts with [Subject 2], where [Subject 2] is presented in realistic full-color style, creating a surreal contrast with the hand-drawn sketch style of [Subject 1] and the background.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 31
  PluginItem(
    name: 'Animal Silicone Wrist Rest',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/f047543ae11e9311c97619f4c0f4c6e85e8f755fa54860efc75cbe9806c6c0f6/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d4e567365347a566867596f4b5347764e5f5f792d6d2e706e673f763d31',
    prompt: 'Create an image of a cute Q-version silicone wrist rest, shaped based on the [🐼] emoji, made of soft food-grade silicone material with a skin-friendly matte texture, filled with slow-rebound cotton inside, anthropomorphic cartoon style with vivid expressions, arms spread open lying on the desktop, presenting a wrist-hugging posture, overall shape is round and soft, colored in [🐼] color scheme, healing and cute style, suitable for office use, white pure color background, soft lighting, product photography style, front view or 45-degree overhead view, high-definition details, highlighting silicone texture and comfort function',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 32
  PluginItem(
    name: 'Glowing Line Anatomy Diagram',
    author: '@umesh_ai',
    imageUrl: 'https://camo.githubusercontent.com/8270aa4a29f6ff2256fd4130a1fceb7b54e6df26269c91001ece8e6d705e6450/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d756b6e5f617a747a413479544e474e46315143546c2e706e673f763d31',
    prompt: 'A digital illustration depicting a [SUBJECT], whose structure is outlined by a set of glowing, clean, and pure blue lines. The image is set against a dark background to highlight the [SUBJECT]\\\'s form and characteristics. A specific part, such as [PART], is emphasized with a red glow to indicate the importance or special significance of that area. The overall style is both educational and visually appealing, designed as if it were an advanced imaging technology.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 33
  PluginItem(
    name: 'Featured City Weather Forecast',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/9253d5b1312c7723cb62b2f0e889cb567cbc1175eb82a5d30895e8cb2b30f123/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d786c397a45753772335644584f5f4f5a474d3161372e706e673f763d31',
    prompt: 'From a clear 45° overhead angle, show an isometric miniature model scene featuring [Shanghai Oriental Pearl Tower, The Bund] and other characteristic city buildings, with weather effects cleverly integrated into the scene, soft cloudy weather gently interacting with the city. Use physically-based realistic rendering (PBR) and realistic lighting effects, solid color background, clear and simple. The composition uses centered framing, highlighting the precise and delicate beauty of the three-dimensional model. Display \"[Shanghai Cloudy 20°C]\" at the top of the image with a cloudy weather icon.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 34
  PluginItem(
    name: 'Translucent Glass Texture Transform',
    author: '@azed_ai',
    imageUrl: 'https://camo.githubusercontent.com/26762f716c908798fdd198713ea346482f703876642dc776e088a11ec621d73c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d5f61446969377072333163376e4243333065734c742e706e673f763d31',
    prompt: 'Transform the attached image into soft 3D translucent glass with frosted matte effects and detailed textures, original colors, centered on a light gray background, gently floating in space, soft shadows, natural lighting',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 35
  PluginItem(
    name: 'Code Style Business Card',
    author: '@umesh_ai',
    imageUrl: 'https://camo.githubusercontent.com/df13c0036d9f88c0374664df82ad0eea0198b765521bb54b127b8437edb6519a/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d59356e785a6374343972356a70444a67657a6968742e706e673f763d31',
    prompt: 'Close-up shot: A hand holding a business card designed to look like a JSON file in VS Code. The code on the card is presented in real JSON syntax highlighting format. The window interface includes typical toolbar icons and title bar, with the title showing as Business Card.json, overall style completely consistent with VS Code interface. Background is slightly blurred, highlighting the business card content.\\nThe JSON code on the business card is as follows:\\n{\\n \"name\": \"Jamez Bondos\",\\n \"title\": \"Your Title\",\\n \"email\": \"<EMAIL>\",\\n \"link\": \"yourwebsite\"\\n}',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 36
  PluginItem(
    name: 'LEGO City Landscape',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/f95d2fab4d47abf6501175038b12d57649e46ff0599cb3ed96ae45bedde81eed/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d4e632d7641686149487148697369637a69525551352e706e673f763d31',
    prompt: 'Create a highly detailed and colorful LEGO version of Shanghai\\\'s Bund scene. The foreground presents the classic Bund historical building complex, using LEGO bricks to exquisitely restore Western and neoclassical architectural facades, including details like clock towers, domes, and colonnades. LEGO minifigures are strolling along the river, taking photos, and sightseeing, with classic LEGO cars parked along the streets. The background is the spectacular Huangpu River, assembled with blue translucent LEGO bricks, with LEGO ferries and tour boats on the river. Across the river, Pudong Lujiazui has towering skyscrapers, including the Oriental Pearl Tower, Shanghai Center, Jinmao Tower, and Shanghai World Financial Center, these ultra-modern LEGO skyscrapers are colorful and realistically shaped. The sky is bright LEGO blue, dotted with a few white LEGO brick clouds, presenting a vibrant and modern visual effect overall.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 37
  PluginItem(
    name: 'Mini 3D Architecture',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/45805585582b815a1605fa805d0243240c1f38067c7d6418c029f9e645fcd413/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d30625170484970696d594c4c72534a3871653643362e706e673f763d31',
    prompt: '3D Q-version mini style, a whimsical mini Starbucks café that looks like a giant takeaway coffee cup, complete with lid and straw. The building has two floors, with large glass windows clearly showing the warm and exquisite interior design: wooden furniture, warm lighting, and busy baristas. There are cute little figurines walking or sitting on the street, surrounded by benches, street lamps, and plant pots, creating a charming corner of the city. The overall style adopts urban miniature landscape style, rich in detail and realistic, with soft lighting presenting a pleasant afternoon feeling.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 38
  PluginItem(
    name: 'Creative Plant Pot',
    author: '@azed_ai',
    imageUrl: 'https://camo.githubusercontent.com/21c44f84989b1176b77bf9bda731611c7b9f7081db4be73c0d67d10bcc9a4d5f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4b4d76665a6b4c4675764d457a437a4254387977722e706e673f763d31',
    prompt: 'A high-quality photo showing a cute ceramic [object/animal] shaped flower pot with a smooth surface, filled with various vibrant succulents and green plants, including spiky haworthia, rosette-shaped echeveria, and delicate white small flowers. The pot has a friendly face, placed on a soft neutral background, using diffused natural lighting, showing delicate textures and color contrasts, with simple composition and extremely minimalist style.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 39
  PluginItem(
    name: '\"Extremely Ordinary\" iPhone Selfie',
    author: '@jiamimaodashu',
    imageUrl: 'https://camo.githubusercontent.com/a6b07bb2170ebd24d3de178a711d25442a1e5373b86895e934961f20519c2950/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d524566655f50666759667a363766356c6961664b752e706e673f763d31',
    prompt: 'Please draw an extremely ordinary iPhone selfie, without clear subject or compositional sense, just like a casual snapshot. The photo has slight motion blur, uneven sunlight or indoor lighting causing slight overexposure. Awkward angle, chaotic composition, overall presenting a deliberately mediocre feeling - like a selfie accidentally taken when pulling the phone out of pocket. The protagonists are Eason Chan and Nicholas Tse, at night, next to Hong Kong Convention and Exhibition Centre, by Victoria Harbour in Hong Kong.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 40
  PluginItem(
    name: 'Glass Material Retexture',
    author: '@egeberkina',
    imageUrl: 'https://camo.githubusercontent.com/f6ea76545847586388ceb6dc749054b2a91be35fe42c51eb9f2e3cdd31337ebc/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d51436453414e324979696779485656706c7058474c2e706e673f763d31',
    prompt: 'retexture the image attached based on the json below:\\n\\n{\\n \"style\": \"photorealistic\",\\n \"material\": \"glass\",\\n \"background\": \"plain white\",\\n \"object_position\": \"centered\",\\n \"lighting\": \"soft, diffused studio lighting\",\\n \"camera_angle\": \"eye-level, straight-on\",\\n \"resolution\": \"high\",\\n \"aspect_ratio\": \"2:3\",\\n \"details\": {\\n \"reflections\": true,\\n \"shadows\": false,\\n \"transparency\": true\\n }\\n}',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 41
  PluginItem(
    name: 'Crystal Ball Story Scene',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/1a623fc0c48774dd44d9ac8749b5ecc2eb91f3b1911eb47f2bc58e08f0442491/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d7463504a594a71576853694c42742d4b4d6e7579442e706e673f763d31',
    prompt: 'An exquisite crystal ball quietly placed on a warm and soft desktop by the window, with a blurred and hazy background in warm tones. Warm sunlight gently penetrates the crystal ball, refracting golden light spots, warmly illuminating the surrounding dim space. Inside the crystal ball naturally presents a miniature three-dimensional world themed on {Chang\\\'e Flying to the Moon}, delicate and beautiful dreamy 3D landscape, with characters and objects all in cute Q-version styling, exquisite and beautiful, full of lively emotional interactions between each other. The overall atmosphere is full of East Asian fantasy colors, extremely rich in detail, presenting a magical realism-like wonderful texture. The entire scene is poetic and dreamlike, magnificent and elegant, radiating warm and soft light, as if given life in the warm light and shadow.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 42
  PluginItem(
    name: 'Nostalgic Anime Movie Poster',
    author: 'photis (Sora)',
    imageUrl: 'https://github.com/JimmyLv/awesome-nano-banana/raw/main/cases/76/example_anime_nostalgic_poster.png',
    prompt: '{The Lord of the Rings} style anime movie poster, with anime art style of \"High School DXD\". The poster shows obvious crease marks from long-term repeated folding, causing physical damage and scratches in wrinkled areas, with colors fading in some places. The surface is covered with irregular creases, fold marks, and scratches, all gradually accumulated during constant moving, like an irreversible entropy increase process continuously expanding.\\\\nHowever, the beautiful memories preserved in our hearts remain intact. When you gaze at this poster full of nostalgic atmosphere, what you feel is the emotional essence carried by those collectibles that have become incredibly precious over time.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 43
  PluginItem(
    name: 'Social Media Frame Fusion',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/fd39233256cad07cd22688a18d7d24d73c81ae825e4571c7d48faaa374bfe4a9/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d386b66617335364a63332d6631354f3839707a4b4e2e706e673f763d31',
    prompt: 'Create a stylized 3D Q-version character based on the attached photo, accurately preserving the person\\\'s facial features and clothing details. The character\\\'s left hand makes a heart gesture (with red heart elements above the fingers), playfully sitting on the edge of a giant Instagram frame with legs hanging outside the frame. The frame top displays username \"Beauty\", with social media icons (likes, comments, shares) floating around.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 44
  PluginItem(
    name: 'Fictional Tweet Screenshot',
    author: '@egeberkina',
    imageUrl: 'https://camo.githubusercontent.com/5b13e965a80e2d8ae3d5889ac3495276a76638dc1edb7d05f60c97841c78ef7f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d354e415041634d4e534c6c376a38507558356d6e742e706e673f763d31',
    prompt: 'Einstein\\\'s ultra-realistic style tweet posted just after completing the theory of relativity. Includes a selfie photo with clearly visible chalkboard and scribbled formulas in the background. The tweet shows Nikola Tesla liked the content below.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 45
  PluginItem(
    name: 'Emoji Tufted Carpet',
    author: '@gizakdag',
    imageUrl: 'https://camo.githubusercontent.com/583b26fdf81dac240b845755312040fd0dae8e85e878bcf32b014ab59e125b4c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d673165446973306430696149454b4c6e6b555361612e706e673f763d31',
    prompt: 'Create an image showing a colorful, hand-tufted carpet shaped like the 🦖 emoji, laid on a minimalist floor background. The carpet design is bold and playful, with soft fluffy texture and thick line details. Shot from above using natural lighting, overall style with a quirky DIY aesthetic. Bright colors, cartoon-like outline, tactile and cozy material—similar to handmade tufted art carpets.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 46
  PluginItem(
    name: 'Colorful Vector Art Poster',
    author: '@michaelrabone',
    imageUrl: 'https://camo.githubusercontent.com/4ffb81b3047df5f83649540ebaeb6cff34583c4b65c43819249b37610500bd77/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d347a38794e2d45564852427468546e704342384b612e706e673f763d31',
    prompt: 'Location is \"London, United Kingdom\", generate a summer colorful vector art poster with large \"LONDON\" title at the top and smaller \"UNITED KINGDOM\" title below',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 47
  PluginItem(
    name: 'Cloud Art',
    author: '@umesh_ai',
    imageUrl: 'https://camo.githubusercontent.com/48ab315960b037955aaa2349972ef99a880c791d9bcc1ada88692691e7b85538/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d75664b5152552d307a58586c4143745f415f444e642e706e673f763d31',
    prompt: 'Generate a photo: capturing a daytime scene where scattered clouds in the sky form the shape of [subject/object], located above [location].',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 48
  PluginItem(
    name: '8-bit Pixel Icon',
    author: '@egeberkina',
    imageUrl: 'https://camo.githubusercontent.com/83e38ad7752cde79777020605aac1e000faec10e8b77ee4dc27a177f30032d6f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4830453845715131306a72626530643871373133372e706e673f763d31',
    prompt: 'Create a minimalist 8-bit pixel style [🍔] logo, centered on a pure white background. Use a limited retro color palette with pixelated details, sharp edges, and clean block forms. The logo should be simple, iconic, and clearly recognizable in pixel art style—inspired by classic arcade game aesthetics.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 49
  PluginItem(
    name: 'Emoji Inflatable Cushion',
    author: '@gizakdag',
    imageUrl: 'https://camo.githubusercontent.com/e5342b40860c8b46ad941a3c14813ea47cddc62c025a8776bc676ef09922574b/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d30514e774a386f725854714c474e4e746f4d3255712e706e673f763d31',
    prompt: 'Create a high-resolution 3D render designing [🥹] as an inflated, bulging object. The shape should be soft, rounded, and air-filled—similar to a plush balloon or inflatable toy. Use smooth matte material with subtle fabric creases and stitching to enhance the inflated effect. The overall form should be slightly irregular and softly sagging, with soft shadows and gentle lighting to highlight volume and realism. Place it on a clean, minimalist background (light gray or light blue), overall style should remain playful yet sculptural.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 50
  PluginItem(
    name: 'Paper Craft Style Emoji Icon',
    author: '@egeberkina',
    imageUrl: 'https://camo.githubusercontent.com/b7a7c89709c5637e3dc6c5f53aa869c009d14009a01057e4862bf055495f18fa/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d536f5a50325a773148435258474f4e46645a5344732e706e673f763d31',
    prompt: 'A paper craft style \"🔥\" icon floating on a pure white background. This emoji is handmade from colored paper cutouts with visible paper texture, creases, and layered shapes. It casts soft shadows below, creating a sense of lightness and dimensionality. The overall design is simple, interesting, and clean, with the image centered and surrounded by ample white space. Use soft studio lighting to highlight paper texture and edges.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 51
  PluginItem(
    name: 'Passport Entry Stamp',
    author: '@M_w14_',
    imageUrl: 'https://camo.githubusercontent.com/9c7ce999c65901c3b9777d79b8df5019e03aaa2ea9a6b7e2c9d2cec432d9fbab/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d75766245316f334b396c38365a57314e31704e4d4c2e706e673f763d31',
    prompt: 'Create a realistic passport page with an entry stamp for [Beijing, China]. The stamp should read \"Welcome to Beijing\" in bold English, designed as circular or oval with decorative borders. The stamp should include \"ARRIVAL\" text and a fictional date like \"April 16, 2025\". Add subtle outlines of {Forbidden City} as background details in the stamp. Use dark blue or red ink with slight bleeding to enhance realism. The stamp should be slightly tilted as if hand-pressed. The passport page should show clear paper texture and security patterns.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 52
  PluginItem(
    name: 'Physical Destruction Effect Card',
    author: '@op7418',
    imageUrl: 'https://camo.githubusercontent.com/9d16d254ef57b11758a6d02d6afbbdf7aaa4a29bb15c934637494d5961df4d0f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d424c69697034377353314d5a327036526f55534a4e2e706e673f763d31',
    prompt: 'An ultra-realistic, cinematic illustration depicting Lara Croft dynamically bursting through the border of an \"Archaeological Adventure\" trading card. She is mid-jump or rope-swinging, wearing iconic adventure gear, possibly using dual pistols with muzzle flashes helping to shatter the card\\\'s ancient stone-carved border, creating visible dimensional rupture effects around the breach, such as energy cracks and spatial distortions, causing dust and debris to scatter. Her body bursts forward with obvious motion depth, breaking through the card\\\'s plane, with the card interior (background) depicting dense jungle ruins or trap-filled ancient tomb interiors. Card debris mixes with crumbling stones, flying vines, ancient coin fragments, and spent shell casings. The \"Archaeological Adventure\" title and \"Lara Croft\" name (with a stylized artifact icon) are visible on the remaining, cracked and weathered portions of the card. Adventure-filled, dynamic lighting highlights her athletic ability and dangerous environment.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 53
  PluginItem(
    name: 'Fashion Magazine Cover Style',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/f06bcee6af14975b53382123ac726fe714fa531b3378e9838a316a62cee318e7/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4b2d7a4d526c7a753379396245724a68356f4444652e706e673f763d31',
    prompt: 'A beautiful woman wearing a pink qipao, adorned with exquisite floral decorations, her hair dotted with colorful flowers, neck decorated with elegant white lace collar. One of her hands gently holds several large butterflies. The overall shooting style presents high-definition detail texture, similar to fashion magazine cover design, with text \"FASHION DESIGN\" marked in the center top of the photo. The background uses simple pure light gray to highlight the main subject.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 54
  PluginItem(
    name: 'Voxel Style 3D Icon Conversion',
    author: '@BrettFromDJ',
    imageUrl: 'https://camo.githubusercontent.com/812429b9b49df01b2df8d17fcc3e5a044eb441489da71bddf1bf499c434a4c94/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d44655052456a4474336e7a366e78745153574374622e706e673f763d31',
    prompt: 'Convert image/description/emoji to voxel 3D icon like the reference image, Octane render, 8k',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 55
  PluginItem(
    name: 'RPG Style Character Card Creation',
    author: '@berryxia_ai',
    imageUrl: 'https://camo.githubusercontent.com/8ff51afd35ba86f0e12ce304f0c651059f5b8ac1bb549150d1fa15bcef58fbcb/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f34342f6578616d706c655f7270675f636172645f64657369676e65722e706e67',
    prompt: 'Create an RPG collectible-style digital character card. Character set as {Programmer}, standing confidently with tools or symbols related to their profession. Presented in 3D cartoon style with soft lighting, showing distinct personality. Add skill bars or attribute values, such as [Skill1 +x], [Skill2 +x], like Creativity +10, UI/UX +8. Add title banner at the top of the card and character nameplate at the bottom. Card border should be clean and sharp, like real collectible figure packaging boxes. Background should match the professional theme. Use warm highlights and colors that match professional characteristics for coloring.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 56
  PluginItem(
    name: 'Q-Version Cute Russian Nesting Dolls',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/0a83915cad66e1b1f35aed3e0e0a65addaa92c82be9373b4df115ed458114b11/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f34332f6578616d706c655f6d617472796f73686b615f706561726c5f65617272696e672e706e67',
    prompt: 'Transform the person in the image into Q-version cute Russian nesting dolls 🪆, five in total from large to small, placed on an exquisite wooden table, banner 3:2 ratio',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 57
  PluginItem(
    name: '3D Family Wedding Photo',
    author: '@balconychy',
    imageUrl: 'https://camo.githubusercontent.com/1f0fae059d027f42d34cf2832eb804d73431e1e98ec118a01395e4ba6f8817a8/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d5a6848674b6b727951655f653632674a794d706a382e706e673f763d31',
    prompt: 'Convert the people in the photo into Q-version 3D characters, parents in wedding attire, child as a beautiful flower girl. Parents in Western wedding attire, father in formal suit, mother in wedding dress. Child holding flowers. Background is a colorful flower arch. Except for the characters being 3D Q-version, everything else in the environment is realistic. The whole scene is placed in a photo frame.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 58
  PluginItem(
    name: '3D Q-Version Couple Snow Globe',
    author: '@balconychy',
    imageUrl: 'https://camo.githubusercontent.com/e99ef2df7acf4c797fadaba52718b901e2634460ac545482fe91b27f9fa62fec/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f34322f6578616d706c655f33645f715f736e6f77676c6f62655f636f75706c652e706e67',
    prompt: 'Convert the characters in the attached image into a snow globe scene. Overall environment: snow globe placed on a desktop by the window, blurred background, warm tones. Sunlight passes through the sphere, casting golden light spots, illuminating the surrounding darkness. Inside the snow globe: characters are cute Q-version 3D styling, full of love in each other\\\'s eyes.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 59
  PluginItem(
    name: 'Miniature Scene (Monkey King vs White Bone Demon)',
    author: '@dotey',
    imageUrl: 'https://github.com/JimmyLv/awesome-nano-banana/raw/main/cases/41/example_miniature_journey_west.png',
    prompt: 'Miniature scene presentation using tilt-shift photography techniques, presenting Q-version [Monkey King vs White Bone Demon] scene',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 60
  PluginItem(
    name: 'Real Object and Hand-drawn Doodle Creative Ad',
    author: '@azed_ai',
    imageUrl: 'https://camo.githubusercontent.com/2b6307f6e906fced7e675614c25fbed6a5e49d47544a050e8e6793a7c2bf0543/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4274303535695734374f557152444f682d4b30675a2e706e673f763d31',
    prompt: 'A minimalist and creative advertisement set on a pure white background. A real [real object] combined with hand-drawn black ink doodles, with loose and playful lines. The doodles depict: [doodle concept and interaction: interacting with the object in clever, imaginative ways]. Add bold black [advertising copy] text at the top or middle. Place [brand logo] clearly at the bottom. The visual effect should be simple, interesting, high contrast, and cleverly conceived.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 61
  PluginItem(
    name: 'Cute Warm Knitted Doll',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/3cc59f2a7296dd0f2e4a8d81e8bfb06dad2c9fb00f92a90589dae15c758d919c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d467056545546574b3264686431475379734f4664682e706e673f763d31',
    prompt: 'A close-up, professionally composed photo showing a hand-crocheted yarn doll being gently held by both hands. The doll has a rounded shape, [uploaded image] character\\\'s cute Q-version image, with bright color contrasts and rich details. The hands holding the doll are natural and gentle, with clear finger postures, natural skin texture and light-shadow transitions, showing warm and realistic touch. Background is slightly blurred, showing indoor environment with warm wooden desktop and natural light from windows, creating a comfortable and intimate atmosphere. The overall image conveys exquisite craftsmanship and cherished warm emotions.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 62
  PluginItem(
    name: 'Japanese Two-Panel Manga',
    author: '@hellokaton',
    imageUrl: 'https://camo.githubusercontent.com/f183d55c3ec78ff3aa0b8ad162592e76660fd6eded824c09ac32ec090e5d3222/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f34302f6578616d706c655f74776f5f70616e656c5f6d616e67615f707265736964656e742e706e67',
    prompt: 'Create a Japanese moe-style two-panel manga, arranged vertically, theme: Girl President\\\'s Work Daily Life. Character image: Convert the uploaded attachment to Japanese moe-style cartoon girl image style, retaining all details of the original image, such as clothing (suit), hairstyle (bright golden yellow), facial features, etc. First panel: - Expression: pitiful and dejected, single hand supporting cheek - Text box: \"What to do! He won\\\'t talk to me! (；´д｀)\" - Scene: warm-toned office, American flag behind, pile of hamburgers on desk, vintage red rotary phone, character on left side of frame, phone on right. Second panel: - Expression: gritting teeth, furious, face flushed red - Action: slamming desk hard, hamburgers bouncing up - Speech bubble: \"Hmph! Double the tariffs! Not talking to me is their loss! ( `д´ )\" - Scene: same as first panel but in chaos. Other notes: - Text uses simple cute handwriting, overall style cute and interesting. - Composition is full and lively, please reserve enough space for text display, appropriate white space. - Image ratio 2:3. - Overall bright colors, highlighting cartoon style.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 63
  PluginItem(
    name: 'Fantasy Cartoon Illustration',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/d50b0d3392e3a7feb4acd0453f8543edaa8ce5823c8287b22980df1bd55b7305/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33392f6578616d706c655f66616e746173795f636f6d70757465725f686561645f706f7274616c2e706e67',
    prompt: 'A cartoon-style character with a head that is a computer monitor with a smiley face, wearing gloves and boots, happily jumping through a glowing blue circular portal, with a lush fantasy forest landscape in the background. The forest is rich in detail with tall trees, mushrooms, flowers, peaceful rivers, floating islands, and an atmospheric starry night sky with multiple moons. Overall using bright vivid colors with soft light effects, fantasy illustration style.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 64
  PluginItem(
    name: 'Hand-drawn Infographic Card (IP Version)',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/2c1ecd488ccc8f41ef612de8d22b7e4a38f159c8c8d49a8fee944c136c8b87eb/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33382f6578616d706c655f68616e645f647261776e5f696e666f677261706869632e706e67',
    prompt: 'Create a hand-drawn style infographic card with a 9:16 vertical ratio. The card has a distinct theme with a beige or off-white background with paper texture, overall design reflecting rustic, friendly hand-drawn aesthetics. The top of the card features red and black contrasting, prominent large brush calligraphy font to highlight the title, attracting visual focus. All text content uses Chinese calligraphy, overall layout divided into 2-4 clear sections, each expressing core points with short, concise Chinese phrases. The font maintains the flowing rhythm of calligraphy, both clear and readable with artistic flair. Appropriate white space around. The card is dotted with simple, interesting hand-drawn illustrations or icons, such as characters or symbolic symbols, to enhance visual appeal and inspire reader thinking and resonance. Overall layout pays attention to visual balance, reserving enough blank space to ensure the picture is simple and clear, easy to read and understand. \"Building IP is long-term compound interest, persist in daily updates, there will definitely be results, because 99% can\\\'t persist!\"',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 65
  PluginItem(
    name: 'Soft Style 3D Advertisement',
    author: '@op7418',
    imageUrl: 'https://camo.githubusercontent.com/d710da544912283c5f4da3e226e61b8ecd5b639442b98572a8df2593ee1decbb/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33372f70617374656c5f706f7765725f33645f6164732e706e67',
    prompt: 'A soft 3D cartoon style [brand product] sculpture made of smooth clay-like texture and bright soft colors, placed in a minimalist isometric scene that complements the product characteristics, simple composition, soft lighting, subtle shadows, with product logo and three-word slogan clearly displayed below.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 66
  PluginItem(
    name: 'Minimalist 3D Illustration',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/3652508db41807adb2fb9055856eb874eb67f8e82ad880ea1fbfb005d0e04340/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33362f6578616d706c655f6d696e696d616c6973745f33645f746f696c65745f7478742e706e67',
    prompt: 'Draw a toilet: ## Art Style Introduction: Minimalist 3D Illustration ### 🎨 Visual Elements #### 🟢 Shape Language - Rounded edges, smooth and soft forms with simplified geometric shapes. #### 🎨 Colors - **Primary colors:** Soft beige, light gray, warm orange. - **Accent colors:** Warm orange for focal elements. - **Shading:** Soft gradients with smooth transitions, avoiding strong shadows and highlights. #### 💡 Lighting - **Type:** Soft, diffused lighting. - **Light source direction:** Above and slightly to the right. - **Shadow style:** Subtle and diffused, no sharp or high-contrast shadows. #### 🧱 Materials - **Surface texture:** Matte, smooth surfaces with subtle shading variations. - **Reflectivity:** Low or none, avoiding obvious gloss. #### 🖼️ Composition - **Object presentation:** Single, centered object with ample negative space around. - **Perspective:** Slightly tilted perspective, presenting moderate three-dimensionality without obvious depth of field effects. - **Background:** Solid color, low saturation, harmonious with the subject without visual interference. #### ✒️ Typography - **Font style:** Minimalist, sans-serif fonts. - **Text position:** Bottom left corner, small size and unobtrusive. - **Font color:** Gray, forming low contrast with background. #### 🖥️ Rendering Style - **Technique:** 3D rendering with simplified low-polygon style. - **Detail level:** Medium detail, focusing on shape and color, avoiding complex textures and details. ### 🎯 Style Goal > Create clean, aesthetically pleasing visual effects emphasizing simplicity, approachability, and modernity.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 67
  PluginItem(
    name: 'Fluffy Emoji Object',
    author: '@gizakdag',
    imageUrl: 'https://camo.githubusercontent.com/add591fcb6adacc9f7250f90ab93e04dc7306ee90b82eab91906246856447465/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33352f6578616d706c655f666c756666795f70756d706b696e2e706e67',
    prompt: 'Transform a simple flat vector icon [🎃] into a soft, three-dimensional, fluffy cute object. The overall shape is completely covered with dense fur, with extremely realistic fur texture and soft shadows. The object is centered and suspended in a clean light gray background, floating lightly. Overall style is surreal, tactile and modern, bringing comfortable and playful visual experience. Using studio-grade lighting, high-resolution rendering, 1:1 ratio.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 68
  PluginItem(
    name: 'Hand-drawn Infographic Card (Cognition Version)',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/d3f1b68d9c2b7cfe363e360a7ef0310c214cad796cc15bb4c98ea32e7698ea6c/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33342f6578616d706c655f68616e645f647261776e5f696e666f677261706869635f636f676e6974696f6e2e706e67',
    prompt: 'Create a hand-drawn style infographic card with a 9:16 vertical ratio. The card has a distinct theme with a beige or off-white background with paper texture, overall design reflecting rustic, friendly hand-drawn aesthetics. The top of the card features red and black contrasting, prominent large brush calligraphy font to highlight the title, attracting visual focus. All text content uses Chinese calligraphy, overall layout divided into 2-4 clear sections, each expressing core points with short, concise Chinese phrases. The font maintains the flowing rhythm of calligraphy, both clear and readable with artistic flair. The card is dotted with simple, interesting hand-drawn illustrations or icons, such as characters or symbolic symbols, to enhance visual appeal and inspire reader thinking and resonance. Overall layout pays attention to visual balance, reserving enough blank space to ensure the picture is simple and clear, easy to read and understand. <h1><span style=\"color:red\">\"Cognition\"</span> determines the ceiling <span style=\"color:red\">\"Circle\"</span> determines opportunities</h1>- You can\'t earn money beyond your \"cognition\", - You can\'t encounter opportunities beyond your \"circle\".',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 69
  PluginItem(
    name: 'Family Wedding Photo Q-Version Conversion',
    author: '@balconychy',
    imageUrl: 'https://camo.githubusercontent.com/3fb5da55ac7d69ecaeb5ffb1c2faba189479a5f43b06dea7e42f0cdb16e8ed42/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33332f6578616d706c655f66616d696c795f77656464696e675f70686f746f5f712e706e67',
    prompt: 'Convert the people in the photo into Q-version 3D characters, parents in wedding attire, child as a beautiful flower girl. Parents in Western wedding attire, father in formal suit, mother in wedding dress. Child holding flowers. Background is a colorful flower arch. Except for the characters being 3D Q-version, everything else in the environment is realistic. The whole scene is placed in a photo frame.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 70
  PluginItem(
    name: 'Titanic Imitation',
    author: '@balconychy',
    imageUrl: 'https://camo.githubusercontent.com/5d29d42b32f595ff9ae827149d4fc057e99488ec9e777dc2f78eefa761a04167/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d70564470535231634c61366b376d6649626d6d4f352e706e673f763d31',
    prompt: 'Convert the characters in the attached image into cute Q-version 3D styling Scene: At the very tip of a luxury cruise ship, the bow is pointed. A man with a woman standing at the bow of the Titanic, the man\'s arms around the woman\'s waist, the woman\'s arms spread wearing a dress, facing the wind, with freedom and joy on her face. At this time the sky shows the warm tones of dusk, the sea extends below the ship. Except for the characters using Q-version 3D styling, all other environments are real objects.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 71
  PluginItem(
    name: 'Folding Paper Sculpture Pop-up Book',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/8bb13880a0cd25a353c4765d6b8310c5bf348914a9d06634328c8e77a9a69a40/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33322f33645f706170657263726166745f706f7075705f626f6f6b2e706e67',
    prompt: 'Multi-layer folding paper sculpture pop-up book, placed on a desk, pure background highlighting the theme, the book presents a three-dimensional flip-book style, 3:2 horizontal ratio. The opened pages present the scene of [Demon Child Nezha vs Ao Bing], all elements can be finely folded and combined, presenting realistic and delicate paper folding texture; composition uniformly adopts front view perspective, overall visual style is dreamy and beautiful, colorful and gorgeous, full of fantastical and vivid story atmosphere.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 72
  PluginItem(
    name: 'Anime Sticker Collection',
    author: '@richardchang',
    imageUrl: 'https://camo.githubusercontent.com/bbd003c3b5e1edffb792e0d7a8260362459d71788fc94beff3f84546ebb23f77/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33312f6578616d706c655f6e617275746f5f737469636b6572732e706e67',
    prompt: 'Create a set of anime-style sticker collection, containing multiple cartoon characters with different expressions and poses, each sticker has white borders, bright colors, unified style.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 73
  PluginItem(
    name: '35mm Film Style Flying Island',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/7d1e1876e70093cc7f306d54d1b034c442975d333584d174a8a5966b9b9673ca/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33302f6578616d706c655f33356d6d5f6d6f73636f775f666c79696e675f69736c616e642e706e67',
    prompt: '35mm film style photo: Moscow floating on a flying island in the sky.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 74
  PluginItem(
    name: 'Famous Portrait OOTD',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/d6a72c9c6f8ef4b074b05edf851d91a6d3a6ee654fd7091305ab1d9e565cf4b5/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f32392f6578616d706c655f706561726c5f65617272696e675f6f6f74642e706e67',
    prompt: 'Generate different professional style OOTDs for the image character, fashionable outfits and accessories, solid color background consistent with character color scheme, Q-version 3d, c4d rendering, maintain facial features, all poses must remain consistent, character proportions with very long legs. Composition: 9:16. Top text: OOTD, left side shows character ootd q-version image, right side shows individual outfit pieces. First profession: Fashion Designer',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 75
  PluginItem(
    name: 'Flat Sticker Design',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/34be2f188d2d9b0d1f406d790ec7ad9fa1db2e095bb43cc3d34bd5886f25536e/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f32382f6578616d706c655f666c61745f737469636b65725f706561726c5f65617272696e672e706e67',
    prompt: 'Design this photo as a minimalist flat illustration style Q-version sticker, thick white border, retain character features, style should be cute, character should extend beyond circular area border, circular area should be solid color without 3d feel, transparent background.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 76
  PluginItem(
    name: 'Q-Version Emoji Sticker Creation',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/7ac18e599a35eba0e9691633130e689c83ae6623d38baff293521fc4a85d2cc1/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d7a3156305a55566f386f6d626b2d4f443134496e4e2e706e673f763d31',
    prompt: 'Create a brand new set of chibi stickers, six unique poses total, with user image as the main character:\\\\n1. Making scissor hands with both hands, playfully winking;\\\\n2. Teary-eyed with slightly trembling lips, showing cute crying expression;\\\\n3. Opening both arms, making an enthusiastic big hug gesture;\\\\n4. Lying on side sleeping, leaning against a mini pillow, with a sweet smile;\\\\n5. Confidently pointing forward with hand, surrounded by sparkling effects;\\\\n6. Blowing a kiss gesture, with heart expressions floating around.\\\\nMaintain chibi aesthetic style: exaggerated expressive big eyes, soft facial lines, lively playful short black hairstyle, paired with bold neckline design white clothing, background using vibrant red, decorated with stars or colorful confetti elements. Appropriate white space around.\\\\nAspect ratio: 9:16',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 77
  PluginItem(
    name: 'Famous Portrait Cereal Advertisement',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/59fa9dc7aacd8eae0a665dd4788cab4e67fcded679534808e64b4316d073da12/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d417775655a65342d4b35546d7346546467574549562e706e673f763d31',
    prompt: '\"Master Cereal\": Based on the character traits of the person in the uploaded photo, generate an oatmeal combination that matches their qualities (such as vegetables, fruits, yogurt, whole grains, etc.) and packaging design, then generate them as the cereal box cover character + corresponding cereal combination advertisement cover. The character must maintain their features, cute Q-version 3d, c4d rendering style. The style of where the cereal is placed should also match the setting, such as in kitchen, supermarket, minimalist design counter, etc. Set it up first, then generate the image.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 78
  PluginItem(
    name: 'Minimalist 3D Illustration (JSON Configuration Version)',
    author: '@0xdlk',
    imageUrl: 'https://camo.githubusercontent.com/2f92af00226c047c8a6af7d7fa71b7bffc866f30ae5678134b97fbf9a7bc4f60/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d466d6e58795f4649413374326f324a4d31416377372e706e673f763d31',
    prompt: 'Generate a toilet using the following JSON configuration file: { \"art_style_profile\": { \"style_name\": \"Minimalist 3D Illustration\", \"visual_elements\": { \"shape_language\": \"Rounded edges, smooth and soft forms with simplified geometry\", \"colors\": { \"primary_palette\": [\"Soft beige, light gray, warm orange\"], \"accent_colors\": [\"Warm orange for focal elements\"], \"shading\": \"Soft gradients with smooth transitions, avoiding harsh shadows or highlights\" }, \"lighting\": { \"type\": \"Soft, diffused lighting\", \"source_direction\": \"Above and slightly to the right\", \"shadow_style\": \"Subtle and diffused, no sharp or high-contrast shadows\" }, \"materials\": { \"surface_texture\": \"Matte, smooth surfaces with subtle shading\", \"reflectivity\": \"Low to none, avoiding glossiness\" }, \"composition\": { \"object_presentation\": \"Single, central object displayed in isolation with ample negative space\", \"perspective\": \"Slightly angled, giving a three-dimensional feel without extreme depth\", \"background\": \"Solid, muted color that complements the object without distraction\" }, \"typography\": { \"font_style\": \"Minimalistic, sans-serif\", \"text_placement\": \"Bottom-left corner with small, subtle text\", \"color\": \"Gray, low-contrast against the background\" }, \"rendering_style\": { \"technique\": \"3D render with simplified, low-poly aesthetics\", \"detail_level\": \"Medium detail, focusing on form and color over texture or intricacy\" } }, \"purpose\": \"To create clean, aesthetically pleasing visuals that emphasize simplicity, approachability, and modernity.\" } }',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 79
  PluginItem(
    name: 'Funko Pop Figure Creation',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/da84e6bb2aa563892aaf37d5c824abd5325d4224231f530dc78c025fb1f0ce02/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d6a437534756d4b5f66356e586d49624a43586838552e706e673f763d31',
    prompt: 'Transform the person in the photo into Funko Pop figure packaging box style, presented in isometric perspective, with the title \"JAMES BOND\" marked on the packaging box. The packaging box displays the character image from the photo, accompanied by the character\\\'s essential items (gun, watch, suit, others). At the same time, the actual figure should also be presented next to the packaging box, using realistic, lifelike rendering style.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 80
  PluginItem(
    name: 'Xiaohongshu Cover Design',
    author: '@balconychy',
    imageUrl: 'https://camo.githubusercontent.com/45b01396b09d1b97bab11b9d4b2c4e332c99365f452fef25d4b10c2fb706f5e9/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d4a726f686e4e795552354e31496e4c525a36692d2d2e706e673f763d31',
    prompt: 'Draw: Create a Xiaohongshu cover. Requirements: Attractive enough to entice users to click; Eye-catching fonts, choose fonts with personality; Text size graded by importance, reflecting the logical structure of the copy; Title should be at least 2 times larger than regular text; Leave white space between text paragraphs. Only use eye-catching colors for text that needs emphasis to attract user attention; Background uses eye-catching patterns (including but not limited to paper, notebook, WeChat chat window, choose one) Use appropriate icons or images to add visual hierarchy, but reduce interference. Copy: Breaking News! ChatGPT has become stronger again! Multi-tasking processing is more powerful✨ Programming capabilities are stronger💪 Creativity is off the charts🎨 Come and try it! Image 9:16 ratio',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 81
  PluginItem(
    name: 'Q-Version Character Emoji Pack Creation',
    author: '@leon_yuan2001',
    imageUrl: 'https://camo.githubusercontent.com/22abf72726920d35002e4036762f2202ca332d2055564e76dade4d2e6eebc8b5/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d624f39614531575976686f397843385845466954582e706e673f763d31',
    prompt: 'Please create a set of Q-version emoji pack with [character from reference image] as the main character, 9 total, arranged in 3x3 grid. Design requirements: - Transparent background. - 1:1 square composition. - Unified Q-version Ghibli cartoon style, bright colors. - Each expression has different actions, expressions, and content, needs to reflect diverse emotions like \"sassy, cheap, cute, crazy\", for example: rolling eyes, pounding ground laughing, soul leaving body, petrified in place, throwing money, eating state, social anxiety attack, etc. Can incorporate office worker and internet meme elements. - Each expression image is complete, no missing parts. - Each expression has unified white outline, presenting sticker effect. - No extra, separate elements in the picture. - Strictly prohibit any text, or ensure text content is accurate (prioritize no text).',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 82
  PluginItem(
    name: 'Figure and Real Person Photo Together',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/02ca170c19bd1e53e2042d9f64da3e8f0d60892087d0627aa411a6a19b57165d/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d6254544e6171486d487a4d4d70773849594d4d34512e706e673f763d31',
    prompt: 'In a casual phone photography style, a [Jackie Chan] anime figure is placed on a desktop with exaggerated and cool actions, fully equipped. At the same time, the corresponding real-world person also appears in the frame, posing in similar gestures to the figure, creating an interesting contrast effect between the figure and real person in the same frame. Overall composition is harmonious and natural, conveying a warm and life-filled visual experience.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 83
  PluginItem(
    name: 'Country 3D Model in Toy Box',
    author: '@TheRelianceAI',
    imageUrl: 'https://camo.githubusercontent.com/1215f2453257e121f2bfa0776ac7b3660740a279a05a0f2f117559939ab5ef5c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d727a4d546f3838353478763348533368565338384d2e706e673f763d31',
    prompt: 'A hyper-realistic overhead photography work showing a 3D printed three-dimensional model inside a beige cardboard box, with the box lid held open by two human hands. Inside the box displays a miniature landscape of [country name], including iconic landmarks, terrain, buildings, rivers, vegetation, and numerous tiny detailed human figure models. This three-dimensional model is full of vivid elements that match geographical features, all made in a tactile, toy-like style using matte 3D printing texture with visible printing layer lines. At the top, the inside of the box lid displays \"[country name]\" in large, brightly colored raised plastic letters—each letter in different colors, all bright colors. The lighting is warm and cinematic, highlighting textures and shadows, creating a sense of realism and charm, as if the viewer is opening a magical miniature version of a country.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 84
  PluginItem(
    name: 'Retro CRT Computer Boot Screen',
    author: '@Gdgtify',
    imageUrl: 'https://camo.githubusercontent.com/adae0abfb4da4e7ab95e1d34e8d2bd2fa8a82bd76da4a7fe1f1db110e31e1886/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4b425365574a4a4231456574514e755641427367312e706e673f763d31',
    prompt: 'Retro CRT computer boot screen, finally displaying ASCII art of [shape or logo].',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 85
  PluginItem(
    name: 'Anime Style Badge',
    author: '@Alittlefatwhale',
    imageUrl: 'https://camo.githubusercontent.com/09dc9c6a5bc182d9a4f1d38668afd09cb1fb100436a1e9d2c476b2ea163b34fb/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d43612d35594a3436793461397373634e61785945752e706e673f763d31',
    prompt: 'Based on the character in the attachment, generate a photo of an anime-style badge, requirements: Material: Tassel Shape: Circular Main subject: One hand holding the badge',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 86
  PluginItem(
    name: '3D Q-Version Chinese Wedding Image',
    author: '@balconychy',
    imageUrl: 'https://camo.githubusercontent.com/7f61e4301bcbc9eb3a7dcbbe569ed2233690a754bf7c704116bee4a79447cf1d/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d784c734937614b347a7956576352774a34366132452e706e673f763d31',
    prompt: 'Convert the two people in the photo into Q-version 3D characters, Chinese ancient costume wedding, bright red color, background with \"囍\" (double happiness) character paper-cut style pattern. Clothing requirements: Realistic, man wearing long robe and mandarin jacket, mainly red, embroidered with golden dragon patterns, showing noble and grand style, with a big red flower on the chest, symbolizing celebration and auspiciousness. Woman wearing Xiuhe dress, also red-based, decorated with exquisite golden patterns and phoenix embroidery, showing elegant and gorgeous feeling, with flower hair accessories on head, adding gentle and graceful temperament. Both are classic attire in Chinese weddings, containing blessings for the couple\'s happy marriage. Headwear requirements: Man: Chinese scholar hat, mainly red, decorated with golden patterns, with exquisite golden ornaments on top, showing traditional elegant and dignified style. Woman: Phoenix crown styling, centered with red flowers, paired with golden three-dimensional decorations and hanging tassels, luxurious and rich, full of classical charm.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 87
  PluginItem(
    name: 'Satirical Poster Generation',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/8196596a8cd42fb558ff6d837c11acc9b045ed52950018f48aecd6f9baea1660/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d78476d4e673168674a59307579467433727a32634b2e706e673f763d31',
    prompt: 'Generate a satirical poster for me: GPT 4o is rolling so hard, everyone stop doing image AI and go deliver food instead',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 88
  PluginItem(
    name: 'One Piece Themed Figure Creation',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/45bd9478d1743c7da4b2e761686a955db15956203a12355a3a6ec32d1a4671fc/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4e7643545151557130354f57366f4b4835717752712e706e673f763d31',
    prompt: 'Transform the person in the photo into One Piece anime themed figure packaging box style, presented in isometric perspective. The packaging box displays the One Piece anime art style design based on the photo character, accompanied by daily essential items (gun, watch, suit and leather shoes). At the same time, the actual figure should also be presented next to the packaging box, using realistic, lifelike rendering style.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 89
  PluginItem(
    name: '3D Q-Version Style Conversion',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/b4bcf766d8e48c5bc7c4182b3139eb084bb7cec7acf2742456f94167dac6170c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d44543877756b783266484863727858516f7361524c2e706e673f763d31',
    prompt: 'Convert the characters in the scene to 3D Q-version style while maintaining the original scene layout and clothing styling unchanged.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 90
  PluginItem(
    name: '3D Couple Jewelry Box Ornament',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/df1beca498c52bcfa41327ebeb14c56763c588c716300d8613539074147d8ebf/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d595758586c5344524d3956547a69512d49413257532e706e673f763d31',
    prompt: 'Based on the photo content, create a delicate, exquisite, cute and adorable 3D rendered collectible ornament, housed in a soft pastel-toned, warm and romantic display box. The display box is light cream colored with soft golden decorations, resembling an exquisite portable jewelry box. Opening the lid reveals a warm and romantic scene: two Q-version characters gazing sweetly at each other. The box top is engraved with \"FOREVER TOGETHER\" surrounded by small delicate stars and heart patterns. Inside the box stands the woman from the photo, holding a small bouquet of white flowers. Beside her is her partner, the man from the photo. Both have large, sparkling, expressive eyes and soft, warm smiles conveying deep love and charming temperament. Behind them is a circular window through which you can see a sunny Chinese classical town skyline and gently floating clouds. The box interior is lit with warm, soft lighting, with floating petals in the background for atmosphere. The entire display box and characters have elegant, harmonious tones, creating a luxurious and dreamy miniature souvenir scene. Size: 9:16',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 91
  PluginItem(
    name: 'PS2 Game Cover Design',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/3f248c16a8ae37a2dd3b180de503c25462acf9782d6461d1b0edf65542857969/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d71564b36666d2d66503861342d6772616e642e6a706567',
    prompt: 'Can you create a PS2 game cover image? Title \"Grand Theft Auto: Far Far Away\". It\\\'s a GTA-style game set in the Shrek universe.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 92
  PluginItem(
    name: 'Satirical Comic Poster',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/b450db45bb2cffbb6e6f42516630155401b25208160f6af68336aa31f2719db3/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d5a383347426f555433447763766f4e3573486d30732e706e673f763d31',
    prompt: 'A satirical comic-style illustration in retro American comic style, background is a multi-tier shelf filled with identical red baseball caps, with large text slogan \"MAKE AMERICA GREAT AGAIN\" printed on the front of the caps, and white labels on the side reading \"MADE IN CHINA\". Close-up perspective focuses on one of the red baseball caps. At the bottom of the image is a price tag with original price \"\$50.00\" crossed out with thick black lines, changed to \"\$77.00\". Color tone is nostalgic earth yellow and dark red, shadow treatment has 90s retro printing texture. Overall composition style is exaggerated and satirical, with satirical implications about political consumerism.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 93
  PluginItem(
    name: 'Minimalist Futuristic Poster',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/5d7cb4d2dcddebaf3c75181114572a68ed32d8b0f19ae8a307b8a64e8e8fdaed/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d44744c41687231316758524c4870723667657744772e706e673f763d31',
    prompt: 'A vertical (3:4) 4K resolution minimalist futuristic exhibition poster with ultra-light cool gray #f4f4f4 background. The poster center features a fluid 3D metaball in the shape of [three-dimensional classic Coca-Cola soda bottle], with frosted glass material and delicate grain noise. Fluid gradient: Coca-Cola red #E41C23 → pearl white #FFFFFF, presenting silky glass texture. High-position softbox soft lighting casts long, soft colored shadows and faint halos. The fluid overlays the text, with obscured letters showing slight Gaussian blur through the frosted glass. Main title \"Coca-Cola\" classic red logo positioned in the center, partially obscured by the unique fluid section; obscured letters show slight Gaussian blur through frosted glass. Subtitle in modern sans-serif bold all-caps pure black font: \"TASTE THE FEELING\" positioned below the main title, also partially covered by fluid creating blur, with remaining parts sharp. Overall clean white space, balanced composition, sharp focus, HDR high dynamic range.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 94
  PluginItem(
    name: 'Futuristic Logo Trading Card',
    author: '@hewarsaber',
    imageUrl: 'https://camo.githubusercontent.com/0afc8cf29d0ba2c44d7c8fb4848e2daecad2716417bcf9d55e68b7de1177d440/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d51474351316a6a754e6f4b59677746446f363171572e706e673f763d31',
    prompt: '{ \"prompt\": \"A futuristic trading card with a dark, moody neon aesthetic and soft sci-fi lighting. The card features a semi-transparent, rounded rectangle with slightly muted glowing edges, appearing as if made of holographic glass. At the center is a large glowing logo of {{logo}}, with no additional text or label, illuminated with a smooth gradient of {{colors}}, but not overly bright. The reflections on the card surface should be subtle, with a slight glossy finish catching ambient light. The background is a dark carbon fiber texture or deep gradient with soft ambient glows bleeding into the edges. Add subtle light rays streaming down diagonally from the top, giving the scene a soft cinematic glow. Apply light motion blur to the edges and reflections to give the scene a sense of depth and energy, as if it\'s part of a high-end tech animation still. Below the card, include realistic floor reflections that mirror the neon edges and logo—slightly diffused for a grounded, futuristic look. Text elements are minimal and softly lit: top-left shows \'{{ticker}}\', top-right has a stylized signature, and the bottom displays \'{{company_name}}\' with a serial number \'{{card_number}}\', a revenue badge reading \'{{revenue}}\', and the year \'{{year}}\'. Typography should have a faint glow with slight blurring, and all elements should feel premium, elegant, and softly illuminated—like a high-end cyberpunk collectible card.\", \"style\": { \"lighting\": \"Neon glow, soft reflections\", \"font\": \"Modern sans-serif, clean and minimal\", \"layout\": \"Centered, structured like a digital collectible card\", \"materials\": \"Glass, holographic plastic, glowing metal edges\" }, \"parameters\": { \"logo\": \"Tesla logo\", \"ticker\": \"TSLA\", \"company_name\": \"Tesla Inc.\", \"card_number\": \"#0006\", \"revenue\": \"\$96.8B\", \"year\": \"2025\", \"colors\": [ \"red\", \"white\", \"dark gray\" ] }, \"medium\": \"3D render, high-resolution digital art\", \"size\": \"1080px by 1080px\" }',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 95
  PluginItem(
    name: 'LEGO Minifigure Collection Display',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/cc9df32f0e08a8453b0b6868196e4ff16b75452235dadc8447b1a6b74b127ddf/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d2d4766547a64575162454d4c44694a69524c5a30542e706e673f763d31',
    prompt: 'Based on the photo I uploaded, generate a vertical ratio photo using the following prompt: Classic LEGO minifigure style, a miniature scene — an animal standing beside me. This animal\\\'s color scheme matches mine. Please create this animal based on your understanding of me (you can choose any animal you think suits me, whether real, surreal, or fantastical, as long as you feel it matches my temperament). The entire scene is set within a transparent glass cube with minimalist staging. The miniature scene base is matte black with silver decorations, simple and stylish. The base has an elegantly engraved nameplate in refined serif font showing the animal\\\'s name. The bottom design also cleverly incorporates biological classification information similar to natural history museum displays, presented in fine etching. Overall composition like a high-end collectible art piece: carefully crafted, curated presentation, detailed lighting. Composition focuses on balance. Background is gradient from dark to light (colors chosen based on main color scheme).',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 96
  PluginItem(
    name: 'ID Photo',
    author: 'LinuxDO@synbio',
    imageUrl: 'https://i.mji.rip/2025/09/04/5258e0b792acebf8096aa4da3462a952.png',
    prompt: 'Extract the portrait head from the image and make it into a 2-inch ID photo, requirements: 1. White background 2. Professional formal wear 3. Front-facing 4. Completely maintain consistent facial features, only changing posture and composition, face still retains original expression, only showing changes in angle and lighting, capturing details of cheekbones, eyebrows, eyes, nose, lips 5. Retain slight facial skin imperfections, don\\\'t over-smooth',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 97
  PluginItem(
    name: 'Personalized Room Design',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/f86750e53827b3ce50daf102593abaa544806ce443edccdc54e759ef06c05977/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d676a6577334e754467686a4364544e34684e6341642e706e673f763d31',
    prompt: 'Generate my room design (bed, bookshelf, sofa, plants, computer desk and computer), with paintings hanging on the wall, city night view outside the window. Cute 3d style, c4d rendering, isometric view.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 98
  PluginItem(
    name: 'Character Crossing Through Portal',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/297007cc08e969ba065b44308e454075552508bbcf677faf519d46619d4e8cd0/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d5f4e4b77475857726e766e326c6b547964394263572e706e673f763d31',
    prompt: 'The 3D Q-version character from the photo crosses through a portal, holding the viewer\\\'s hand, dynamically looking back while pulling the viewer forward. The background outside the portal is the viewer\\\'s real world, a typical programmer\\\'s study with desk, monitor and laptop. Inside the portal is the character\\\'s 3D Q-version world, details can reference the photo, overall blue tone, forming sharp contrast with the real world. The portal emits mysterious blue and purple tones, is a perfect elliptical frame between two worlds positioned in the center of the image. Shot from third-person perspective camera angle, showing the viewer\\\'s hand being pulled into the character\\\'s world. 2:3 aspect ratio.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 99
  PluginItem(
    name: 'Keyboard ESC Keycap Miniature 3D Model',
    author: '@egeberkina',
    imageUrl: 'https://camo.githubusercontent.com/9f0c33dd9099b066abdd4c0ac9576849e8e19aeef0abce2d7e7f68b0155d5f7f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4f6473416132526b67336555376162616b47664e5a2e706e673f763d31',
    prompt: 'A hyper-realistic isometric 3D render showing a miniature computer workspace housed within a semi-transparent mechanical keyboard keycap, specifically placed on the ESC key of a real matte-surface mechanical keyboard. Inside the keycap, a small person in comfortable, textured hoodie sits in a modern ergonomic chair, focused on working at a glowing, hyper-realistic computer screen. The entire space is filled with realistic miniature tech accessories: real-material desk lamp, reflective monitor, tiny speaker grilles, tangled cables, and ceramic mug. The scene bottom is composed of soil, rocks and moss with photographic material texture and natural imperfections. Lighting inside the keycap simulates morning natural sunlight, casting soft shadows and warm tones; while outside the keycap is influenced by cool-toned reflections from the surrounding keyboard environment. \"ESC\" text is etched on the semi-transparent keycap top with subtle frosted glass effect—only faintly visible depending on viewing angle. Surrounding keys like F1, Q, Shift and CTRL are clearly visible with realistic material textures and lighting. Overall image appears shot by high-end phone camera with shallow depth of field, perfect white balance and cinematic details.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 100
  PluginItem(
    name: 'Hyper-realistic 3D Game',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://camo.githubusercontent.com/55783ce93783423b98d2172c6ef9effd875a9d09280f27ba1c0d690ebb9b56ae/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d726c7072726d7067725759787a6347464736634e352e706e673f763d31',
    prompt: 'Hyper-realistic 3D rendered scene recreating Natasha\'s character design from 2008\'s \"Command & Conquer: Red Alert 3\", completely following the original modeling. Scene set in a dim, cluttered 2008-era bedroom, character sitting on carpet facing an old TV and game console controller playing \"Command & Conquer: Red Alert 3\". The entire room is filled with 2008 nostalgic atmosphere: snack wrappers, soda cans, posters, and tangled wires. Natasha Volkova is captured in the moment of turning her head, looking back at the camera, her iconic ethereal beautiful face showing a pure innocent smile. Her upper body slightly twisted, movement natural, as if just startled by the flash and reacting. The flash slightly overexposes her face and clothes, making her silhouette stand out more in the dim room. The entire photo appears raw and natural, strong light-dark contrast casts deep shadows behind her, the image is full of tactile feeling, with authentic 2008 film snapshot analog texture.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 101
  PluginItem(
    name: 'Creative Silk Universe',
    author: '@ZHO_ZHO_ZHO',
    imageUrl: 'https://github.com/JimmyLv/awesome-nano-banana/blob/main/cases/66/example_silk_creation_universe.png?raw=true',
    prompt: 'Transform {❄️} into a soft 3D silk-textured object. The entire object surface is wrapped in smooth flowing silk fabric with surreal wrinkle details, soft highlights and shadows. The object gently floats in the center of a clean light gray background, creating a light and elegant atmosphere. Overall style is surreal, tactile and modern, conveying comfortable and refined playful feeling. Studio lighting, high-resolution rendering.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 102
  PluginItem(
    name: 'Fantasy Underwater Scene Popsicle',
    author: '@madpencil_',
    imageUrl: 'https://camo.githubusercontent.com/1aa5213feeea6cc3523485a64ad969460b11922621efc8983854ef0f3f19489a/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d736c654d554a47305f736d665968554a3078397a6b2e706e673f763d31',
    prompt: 'Tilted first-person perspective shot, one hand holding a surreal popsicle. The popsicle has a transparent blue shell inside showing an underwater scene: a small diver, several small fish and floating bubbles, plus rolling waves, with a green popsicle stick running through the center. The popsicle is slightly melting, bottom has a wooden stick, hand is gripping this wooden stick. Background is soft-focus New York street scene, using high-end product photography style.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 103
  PluginItem(
    name: 'Steampunk Mechanical Fish',
    author: '@f-is-h',
    imageUrl: 'https://camo.githubusercontent.com/32ff5cbbf59d055b08ba4e578d90692fb0f48c3cc8128177e63006a9179fd8aa/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4e774b48536b6f62475a7551756154667a596e6b6b2e706e673f763d31',
    prompt: 'A steampunk-style mechanical fish with brass-style body, clearly showing mechanical gear structure during movement. Can slightly see its mechanical teeth, neat and tightly closed, both upper and lower teeth visible. Each tooth is triangular, made of diamond material. Tail fin is metal wire weaving structure, other fin parts are semi-transparent amber-colored glass with some subtle bubbles inside. Eyes are multi-faceted rubies, clearly showing their reflected luster. The fish clearly shows \"f-is-h\" text on its body, with all letters lowercase, paying attention to hyphen positions. Image is square, entire frame shows the full fish body in the center, fish head facing right, with appropriate white space so the frame isn\'t cramped, leaving more space on left and right sides. Background has faint steampunk-style gear textures. The entire fish looks very cool. This is a high-definition image with very rich details throughout the photo, with unique texture and beauty. The image shouldn\'t be too dark.',
    mode: 'Generate',
    isFavorite: false,
  ),
  // 104
  PluginItem(
    name: 'Polaroid Photo Breaking Out Effect',
    author: '@dotey',
    imageUrl: 'https://camo.githubusercontent.com/7f362f5b4cf2ada98d76ee033f5fa18226c5ab99c5a2188ae8c897039d654bfa/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d5f6372464d6f353774544a317474506b50437465312e706e673f763d31',
    prompt: 'Convert the character in the scene to 3D Q-version style, placed on a Polaroid photo, the photo paper held by one hand, the character in the photo is walking out from the Polaroid photo, presenting a visual effect of breaking through the two-dimensional photo frame and entering three-dimensional reality space.',
    mode: 'Edit',
    isFavorite: false,
  ),
  // 105
  PluginItem(
    name: '3D Interior Design Scene',
    author: 'LinuxDO@freebsdfx',
    imageUrl: 'https://i.mji.rip/2025/09/04/bf081bdb4072953231fec5dd24bbf1b6.png',
    prompt: 'A highly realistic image of modern architectural presentation. A detailed 2D black and white floor plan is laid flat on large white paper, with technical lines and dimensions drawn. Above this plan, a 3D rendering of the same apartment floats hologram-like and semi-transparent, photorealistically rendered. The 3D model shows living room, kitchen, bedroom with furniture, with clear textures and materials. The contrast between the flat plan below and the completed 3D model above emphasizes the design process \"from drawing to reality\". Soft ambient lighting, subtle shadows, reflections on paper add realism. Ultra-high resolution. Photorealistic style. Architectural visualization. Cinematic composition. Elegant and futuristic mood.',
    mode: 'Edit',
    isFavorite: false,
  ),*/
];